'use client';

import { CompetitorDashboard } from '../../components/CompetitorDashboard';
import { AppLayout } from '../../components/layout/AppLayout';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { UsersIcon, PlusIcon } from 'lucide-react';

export default function CompetitorsPage() {
  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center mb-2">
              <UsersIcon className="w-8 h-8 text-purple-600 mr-3" />
              <h1 className="text-3xl font-bold text-gray-900">Competitor Tracking</h1>
            </div>
            <p className="text-gray-600">
              Monitor your competitors and discover winning strategies with AI-powered insights.
            </p>
          </div>
          <Button intent="primary" data-rounded="default">
            <PlusIcon className="w-4 h-4 mr-2" />
            Add Competitor
          </Button>
        </div>
        
        <CompetitorDashboard />
      </div>
    </AppLayout>
  );
}