import NextAuth from 'next-auth'
import type { NextAuthOptions } from 'next-auth'

// Custom Whop OAuth Provider
const WhopProvider = {
  id: 'whop',
  name: 'Whop',
  type: 'oauth' as const,
  authorization: {
    url: 'https://whop.com/oauth',
    params: {
      scope: 'read_user',
      response_type: 'code',
    },
  },
  token: {
    url: 'https://api.whop.com/api/v5/oauth/token',
    async request(context: any) {
      const { provider, params, checks, client } = context;
      
      // Use client_secret_post method for token exchange
      const response = await fetch(provider.token.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
        },
        body: new URLSearchParams({
          grant_type: 'authorization_code',
          client_id: provider.clientId!,
          client_secret: provider.clientSecret!,
          code: params.code!,
          redirect_uri: params.redirect_uri || '',
          ...(checks.state && { state: checks.state }),
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Token exchange failed:', response.status, errorText);
        throw new Error(`Token exchange failed: ${response.status} ${errorText}`);
      }

      const tokens = await response.json();
      return { tokens };
    },
  },
  userinfo: 'https://api.whop.com/api/v5/me',
  clientId: process.env.WHOP_CLIENT_ID,
  clientSecret: process.env.WHOP_CLIENT_SECRET,
  profile(profile: any) {
    return {
      id: profile.id,
      name: profile.username,
      email: profile.email,
      image: profile.profile_pic_url,
      whopId: profile.id,
    }
  },
}

const authOptions: NextAuthOptions = {
  providers: [WhopProvider],
  secret: process.env.NEXTAUTH_SECRET,
  callbacks: {
    async jwt({ token, account, profile }) {
      // Persist the OAuth access_token and whop user info to the token right after signin
      if (account) {
        token.accessToken = account.access_token
        token.whopId = (profile as any)?.id
      }
      return token
    },
    async session({ session, token }) {
      // Send properties to the client
      if (session.user) {
        session.user.id = token.whopId as string
        session.user.whopId = token.whopId as string
        session.accessToken = token.accessToken as string
      }
      return session
    },
    async signIn({ user, account, profile }) {
      if (account?.provider === 'whop' && profile) {
        try {
          // Create or update user in our database
          const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'https://api-production-7bd1.up.railway.app'
          
          // Add timeout to prevent hanging
          const controller = new AbortController()
          const timeoutId = setTimeout(() => controller.abort(), 5000) // 5 second timeout
          
          const response = await fetch(`${apiUrl}/api/auth/whop/user`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              whopId: (profile as any).id,
              email: (profile as any).email,
              name: (profile as any).username,
              image: (profile as any).profile_pic_url,
            }),
            signal: controller.signal,
          })
          
          clearTimeout(timeoutId)

          if (!response.ok) {
            console.error('Failed to create/update user:', response.status, await response.text())
            // Don't block authentication if user creation fails
            // The user can still sign in, we'll handle user creation later
          } else {
            console.log('User successfully created/updated in database')
          }
        } catch (error) {
          console.error('Error creating/updating user:', error)
          // Don't block authentication if user creation fails
          // The user can still sign in, we'll handle user creation later
        }
      }
      // Always return true to allow authentication to proceed
      return true
    },
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  session: {
    strategy: 'jwt',
  },
}

const handler = NextAuth(authOptions)

export { handler as GET, handler as POST }