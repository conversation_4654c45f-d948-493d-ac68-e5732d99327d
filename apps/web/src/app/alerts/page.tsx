'use client';

import { useState } from 'react';
import { AppLayout } from '../../components/layout/AppLayout';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Badge from '../../components/ui/Badge';
import { 
  BellIcon, 
  PlusIcon, 
  SearchIcon,
  FilterIcon,
  TrendingUpIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  XCircleIcon
} from 'lucide-react';

export default function AlertsPage() {
  const [activeTab, setActiveTab] = useState('active');

  const alerts = [
    {
      id: 1,
      title: 'Wireless Earbuds trending up',
      description: 'Product has exceeded 75% trend score threshold',
      type: 'trending',
      status: 'active',
      timestamp: '2 hours ago',
      trendScore: 82
    },
    {
      id: 2,
      title: 'New competitor detected',
      description: '@competitor_user started promoting similar products',
      type: 'competitor',
      status: 'active',
      timestamp: '4 hours ago',
      trendScore: null
    },
    {
      id: 3,
      title: 'Price drop alert',
      description: 'Smart Watch price dropped by 15%',
      type: 'price',
      status: 'dismissed',
      timestamp: '1 day ago',
      trendScore: 68
    },
    {
      id: 4,
      title: 'Commission rate increased',
      description: 'Phone Case commission increased to 12%',
      type: 'commission',
      status: 'active',
      timestamp: '2 days ago',
      trendScore: 71
    }
  ];

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'trending':
        return <TrendingUpIcon className="w-5 h-5 text-green-600" />;
      case 'competitor':
        return <AlertTriangleIcon className="w-5 h-5 text-yellow-600" />;
      case 'price':
        return <TrendingUpIcon className="w-5 h-5 text-blue-600" />;
      case 'commission':
        return <TrendingUpIcon className="w-5 h-5 text-purple-600" />;
      default:
        return <BellIcon className="w-5 h-5 text-gray-600" />;
    }
  };

  const getAlertBadge = (type: string) => {
    switch (type) {
      case 'trending':
        return <Badge variant="soft" intent="success" size="sm">Trending</Badge>;
      case 'competitor':
        return <Badge variant="soft" intent="warning" size="sm">Competitor</Badge>;
      case 'price':
        return <Badge variant="soft" intent="primary" size="sm">Price</Badge>;
      case 'commission':
        return <Badge variant="soft" intent="secondary" size="sm">Commission</Badge>;
      default:
        return <Badge variant="soft" intent="gray" size="sm">Alert</Badge>;
    }
  };

  const filteredAlerts = alerts.filter(alert => 
    activeTab === 'all' || alert.status === activeTab
  );

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center mb-2">
              <BellIcon className="w-8 h-8 text-yellow-600 mr-3" />
              <h1 className="text-3xl font-bold text-gray-900">Alerts</h1>
            </div>
            <p className="text-gray-600">
              Stay informed about trending products and market opportunities
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button intent="gray" variant="outlined" data-rounded="default">
              <FilterIcon className="w-4 h-4 mr-2" />
              Filters
            </Button>
            <Button intent="primary" data-rounded="default">
              <PlusIcon className="w-4 h-4 mr-2" />
              New Alert
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <Card className="p-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex-1 relative">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search alerts..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                data-rounded="default"
              />
            </div>
            <div className="flex items-center gap-2">
              <Button
                intent={activeTab === 'active' ? 'primary' : 'gray'}
                variant={activeTab === 'active' ? 'solid' : 'ghost'}
                size="sm"
                onClick={() => setActiveTab('active')}
                data-rounded="default"
              >
                Active ({alerts.filter(a => a.status === 'active').length})
              </Button>
              <Button
                intent={activeTab === 'dismissed' ? 'primary' : 'gray'}
                variant={activeTab === 'dismissed' ? 'solid' : 'ghost'}
                size="sm"
                onClick={() => setActiveTab('dismissed')}
                data-rounded="default"
              >
                Dismissed ({alerts.filter(a => a.status === 'dismissed').length})
              </Button>
              <Button
                intent={activeTab === 'all' ? 'primary' : 'gray'}
                variant={activeTab === 'all' ? 'solid' : 'ghost'}
                size="sm"
                onClick={() => setActiveTab('all')}
                data-rounded="default"
              >
                All ({alerts.length})
              </Button>
            </div>
          </div>
        </Card>

        {/* Alerts List */}
        <div className="space-y-4">
          {filteredAlerts.map((alert) => (
            <Card key={alert.id} className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-4 flex-1">
                  <div className="p-2 bg-gray-50 rounded-lg">
                    {getAlertIcon(alert.type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-semibold text-gray-900">{alert.title}</h3>
                      {getAlertBadge(alert.type)}
                      {alert.trendScore && (
                        <Badge variant="outlined" intent="primary" size="sm">
                          Score: {alert.trendScore}
                        </Badge>
                      )}
                    </div>
                    <p className="text-gray-600 mb-2">{alert.description}</p>
                    <p className="text-sm text-gray-500">{alert.timestamp}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2 ml-4">
                  {alert.status === 'active' ? (
                    <>
                      <Button intent="success" variant="ghost" size="sm" data-rounded="default">
                        <CheckCircleIcon className="w-4 h-4" />
                      </Button>
                      <Button intent="danger" variant="ghost" size="sm" data-rounded="default">
                        <XCircleIcon className="w-4 h-4" />
                      </Button>
                    </>
                  ) : (
                    <Badge variant="soft" intent="gray" size="sm">
                      Dismissed
                    </Badge>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Alert Settings */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Alert Settings</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Trend Score Threshold
                </label>
                <input
                  type="range"
                  min="50"
                  max="100"
                  defaultValue="75"
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-500 mt-1">
                  <span>50</span>
                  <span>75</span>
                  <span>100</span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price Change Threshold
                </label>
                <select className="w-full border border-gray-300 rounded-lg px-3 py-2" data-rounded="default">
                  <option value="5">5% change</option>
                  <option value="10">10% change</option>
                  <option value="15">15% change</option>
                  <option value="20">20% change</option>
                </select>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Email Notifications</span>
                <input type="checkbox" defaultChecked className="rounded" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Push Notifications</span>
                <input type="checkbox" defaultChecked className="rounded" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Daily Summary</span>
                <input type="checkbox" className="rounded" />
              </div>
            </div>
          </div>
          <div className="flex justify-end mt-6">
            <Button intent="primary" data-rounded="default">
              Save Settings
            </Button>
          </div>
        </Card>
      </div>
    </AppLayout>
  );
}