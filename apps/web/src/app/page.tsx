'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { TrendingUpIcon, BellIcon, BarChart3Icon, UsersIcon, ArrowRightIcon } from 'lucide-react';
import { WhopLoginButton } from '@/components/auth/WhopLoginButton';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import Badge from '@/components/ui/Badge';

export default function Home() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    // If user is authenticated, redirect to dashboard
    if (mounted && status === 'authenticated') {
      router.push('/dashboard');
    }
  }, [mounted, status, router]);

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // Show loading state while checking authentication
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // Show landing page for unauthenticated users
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-blue-100">
      {/* Simple header for landing page */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center gap-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary-500">
                <span className="text-sm font-bold text-white">X</span>
              </div>
              <div>
                <span className="text-xl font-bold text-gray-900">Xact Data</span>
                <span className="ml-2 text-xs text-gray-500">Creator OS</span>
              </div>
            </div>
            <WhopLoginButton />
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center">
          <Badge variant="soft" intent="primary" size="lg" className="mb-6">
            🚀 Now Live: AI-Powered Creator OS
          </Badge>
          <h2 className="text-4xl font-extrabold text-gray-900 sm:text-6xl">
            The Ultimate Operating System for
            <span className="text-primary-500"> TikTok Shop Creators</span>
          </h2>
          <p className="mt-6 text-xl text-gray-500 max-w-3xl mx-auto">
            Discover winning products, track competitors, and maximize your revenue with AI-powered insights
            and real-time alerts.
          </p>
          <div className="mt-10 flex gap-4 justify-center">
            <WhopLoginButton className="px-8 py-3 text-lg">
              Get Started Free
            </WhopLoginButton>
            <Button intent="primary" variant="outlined" size="lg" data-rounded="default">
              View Demo
              <ArrowRightIcon className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>

        {/* Features Grid */}
        <div className="mt-20 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
          <Card className="p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mb-4">
              <TrendingUpIcon className="w-6 h-6 text-green-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Winning Products</h3>
            <p className="text-gray-500">
              Real-time product discovery with trend scores, commission rates, and performance metrics.
            </p>
          </Card>

          <Card className="p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-lg mb-4">
              <BellIcon className="w-6 h-6 text-yellow-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Live Alerts</h3>
            <p className="text-gray-500">
              Instant notifications when products start trending above your custom thresholds.
            </p>
          </Card>

          <Card className="p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center justify-center w-12 h-12 bg-primary-100 rounded-lg mb-4">
              <BarChart3Icon className="w-6 h-6 text-primary-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Affiliate Dashboard</h3>
            <p className="text-gray-500">
              Track your performance, set goals, and get AI-powered growth recommendations.
            </p>
          </Card>

          <Card className="p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mb-4">
              <UsersIcon className="w-6 h-6 text-purple-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Competitor Tracking</h3>
            <p className="text-gray-500">
              Monitor competitors and get AI-generated playbooks to outperform them.
            </p>
          </Card>
        </div>

        {/* Stats Section */}
        <Card className="mt-20 p-8">
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-3">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-500">10K+</div>
              <div className="text-gray-500">Products Tracked</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-500">500+</div>
              <div className="text-gray-500">Active Creators</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-500">$2M+</div>
              <div className="text-gray-500">Creator Revenue</div>
            </div>
          </div>
        </Card>
      </main>
    </div>
  );
}