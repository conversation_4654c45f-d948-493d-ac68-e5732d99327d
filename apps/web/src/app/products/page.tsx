'use client';

import { useState, useEffect } from 'react';
import { TrendingUpIcon, ExternalLinkIcon, SearchIcon, FilterIcon } from 'lucide-react';
import { Product, ApiResponse } from '@xact-data/shared';
import { WinningProductsTable } from '../../components/WinningProductsTable';
import { ProductFilters } from '../../components/ProductFilters';
import { AppLayout } from '../../components/layout/AppLayout';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import Badge from '../../components/ui/Badge';
import { createApiUrl } from '../../lib/api';

interface ProductsResponse {
  products: Product[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export default function ProductsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  type SortableFields = 'trendScore' | 'soldIn24h' | 'estimatedGMV' | 'commissionRate';
  
  const [filters, setFilters] = useState({
    category: '',
    minTrendScore: 0,
    sortBy: 'trendScore' as SortableFields,
    sortOrder: 'desc' as 'asc' | 'desc',
    page: 1,
    limit: 20,
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== '' && value !== 0) {
          queryParams.append(key, value.toString());
        }
      });

      const response = await fetch(createApiUrl(`api/products?${queryParams}`));
      const data: ApiResponse<ProductsResponse> = await response.json();

      if (data.success && data.data) {
        setProducts(data.data.products);
        setPagination(data.data.pagination);
      } else {
        setError(data.error || 'Failed to fetch products');
      }
    } catch (err) {
      setError('Failed to connect to the server');
      console.error('Error fetching products:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, [filters]);

  const handleFilterChange = (newFilters: Partial<typeof filters>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  if (loading) {
    return (
      <AppLayout>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-8"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <Card className="p-8 text-center">
            <div className="text-red-500 text-xl mb-4">Error loading products</div>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button 
              intent="primary"
              onClick={fetchProducts}
              data-rounded="default"
            >
              Retry
            </Button>
          </Card>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center mb-2">
              <TrendingUpIcon className="w-8 h-8 text-green-600 mr-3" />
              <h2 className="text-3xl font-bold text-gray-900">Winning Products</h2>
            </div>
            <p className="text-gray-600">
              Discover trending products with real-time metrics and performance data
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button intent="gray" variant="outlined" data-rounded="default">
              <FilterIcon className="w-4 h-4 mr-2" />
              Filters
            </Button>
            <Button intent="primary" data-rounded="default">
              Export Data
            </Button>
          </div>
        </div>

        {/* Filters */}
        <ProductFilters 
          filters={filters}
          onFilterChange={handleFilterChange}
        />

        {/* Stats Bar */}
        <Card className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900">{pagination.total}</div>
              <div className="text-sm text-gray-500">Total Products</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">
                {products.filter(p => (p.trendScore ?? 0) >= 70).length}
              </div>
              <div className="text-sm text-gray-500">Trending Now</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-500">
                {products.length > 0 ? Math.round(products.reduce((acc, p) => acc + (p.commissionRate ?? 0), 0) / products.length) : 0}%
              </div>
              <div className="text-sm text-gray-500">Avg Commission</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">
                ${products.length > 0 ? Math.round(products.reduce((acc, p) => acc + (p.estimatedGMV ?? 0), 0) / products.length).toLocaleString() : 0}
              </div>
              <div className="text-sm text-gray-500">Avg GMV</div>
            </div>
          </div>
        </Card>

        {/* Products Table */}
        <Card>
          <WinningProductsTable 
            products={products}
            pagination={pagination}
            onPageChange={handlePageChange}
            onSort={(sortBy, sortOrder) => handleFilterChange({ sortBy, sortOrder })}
            currentSort={{ sortBy: filters.sortBy, sortOrder: filters.sortOrder }}
          />
        </Card>
      </div>
    </AppLayout>
  );
}