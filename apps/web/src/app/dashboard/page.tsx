'use client';

import { AppLayout } from '../../components/layout/AppLayout';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Badge from '../../components/ui/Badge';
import { AreaChart } from '../../components/charts/AreaChart';
import { BarChart } from '../../components/charts/BarChart';
import { 
  TrendingUpIcon, 
  DollarSignIcon, 
  ShoppingCartIcon, 
  UsersIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PlusIcon,
  RefreshCwIcon
} from 'lucide-react';

// Mock data for charts
const revenueData = [
  { name: 'Jan', value: 4000 },
  { name: 'Feb', value: 3000 },
  { name: 'Mar', value: 5000 },
  { name: 'Apr', value: 4500 },
  { name: 'May', value: 6000 },
  { name: 'Jun', value: 5500 },
  { name: 'Jul', value: 7000 },
];

const ordersData = [
  { name: 'Mon', value: 120 },
  { name: '<PERSON><PERSON>', value: 150 },
  { name: 'Wed', value: 180 },
  { name: 'Thu', value: 200 },
  { name: 'Fri', value: 250 },
  { name: 'Sat', value: 300 },
  { name: 'Sun', value: 280 },
];

export default function DashboardPage() {
  return (
    <AppLayout>
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600 mt-2">
              Visualize your main activities data
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button intent="gray" variant="outlined" size="sm" className="rounded-xl">
              <RefreshCwIcon className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button intent="primary" size="sm" className="rounded-xl">
              <PlusIcon className="h-4 w-4 mr-2" />
              New Campaign
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="p-6 rounded-2xl">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500 mb-1">New Orders</p>
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold text-gray-900">639400</span>
                  <Badge variant="soft" intent="success" size="sm" className="rounded-lg">
                    <ArrowUpIcon className="h-3 w-3 mr-1" />
                    32%
                  </Badge>
                </div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-blue-100">
                <ShoppingCartIcon className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </Card>

          <Card className="p-6 rounded-2xl">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500 mb-1">New Customers</p>
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold text-gray-900">478000</span>
                </div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-green-100">
                <UsersIcon className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </Card>

          <Card className="p-6 rounded-2xl">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500 mb-1">Revenue</p>
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold text-gray-900">$125,430</span>
                  <Badge variant="soft" intent="success" size="sm" className="rounded-lg">
                    <ArrowUpIcon className="h-3 w-3 mr-1" />
                    18%
                  </Badge>
                </div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-primary-100">
                <DollarSignIcon className="h-6 w-6 text-primary-600" />
              </div>
            </div>
          </Card>

          <Card className="p-6 rounded-2xl">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500 mb-1">Growth Rate</p>
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold text-gray-900">24.5%</span>
                  <Badge variant="soft" intent="warning" size="sm" className="rounded-lg">
                    <ArrowDownIcon className="h-3 w-3 mr-1" />
                    2%
                  </Badge>
                </div>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-yellow-100">
                <TrendingUpIcon className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="p-6 rounded-2xl">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">New Orders</h3>
                <p className="text-sm text-gray-500">Visualize your main activities data</p>
              </div>
              <Button intent="gray" variant="ghost" size="sm" className="rounded-xl">
                View Details
              </Button>
            </div>
            <AreaChart data={revenueData} height={250} color="#007FFF" />
          </Card>

          <Card className="p-6 rounded-2xl">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Weekly Orders</h3>
                <p className="text-sm text-gray-500">Visualize your main activities data</p>
              </div>
              <Button intent="gray" variant="ghost" size="sm" className="rounded-xl">
                View Details
              </Button>
            </div>
            <BarChart data={ordersData} height={250} color="#10b981" />
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="p-6 rounded-2xl lg:col-span-2">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
              <Button intent="gray" variant="ghost" size="sm" className="rounded-xl">
                View All
              </Button>
            </div>
            <div className="space-y-4">
              {[1, 2, 3, 4].map((item) => (
                <div key={item} className="flex items-center gap-4 p-3 rounded-xl bg-gray-50">
                  <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-primary-100">
                    <ShoppingCartIcon className="h-5 w-5 text-primary-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">New order received</p>
                    <p className="text-xs text-gray-500">2 minutes ago</p>
                  </div>
                  <Badge variant="soft" intent="success" size="sm" className="rounded-lg">
                    +$125
                  </Badge>
                </div>
              ))}
            </div>
          </Card>

          <Card className="p-6 rounded-2xl">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Quick Stats</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Conversion Rate</span>
                <span className="text-sm font-medium text-gray-900">3.2%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Avg. Order Value</span>
                <span className="text-sm font-medium text-gray-900">$89.50</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Customer Retention</span>
                <span className="text-sm font-medium text-gray-900">68%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Active Campaigns</span>
                <span className="text-sm font-medium text-gray-900">12</span>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
