'use client';

import { useState } from 'react';
import { 
  BrainIcon, 
  LightbulbIcon,
  TrendingUpIcon,
  AlertCircleIcon,
  CheckCircleIcon,
  RefreshCwIcon
} from 'lucide-react';
import { apiPut } from '../../lib/api';

interface AIInsight {
  id: string;
  insightType: string;
  title: string;
  content: string;
  priority: string;
  actionItems?: string[];
  isRead: boolean;
  createdAt: string;
}

interface AIInsightsSectionProps {
  insights: {
    unread: AIInsight[];
    priority: AIInsight[];
  };
  userId: string;
  onGenerateInsight: (type: string) => void;
  onInsightUpdate: () => void;
}

export function AIInsightsSection({ insights, userId, onGenerateInsight, onInsightUpdate }: AIInsightsSectionProps) {
  const [selectedInsight, setSelectedInsight] = useState<AIInsight | null>(null);

  const markAsRead = async (insightId: string) => {
    try {
      const response = await apiPut(`api/affiliate-dashboard/user/${userId}/insights/${insightId}/read`, {});
      const data = await response.json();
      
      if (data.success) {
        onInsightUpdate();
      }
    } catch (error) {
      console.error('Failed to mark insight as read:', error);
    }
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'PERFORMANCE_SUMMARY': return TrendingUpIcon;
      case 'GROWTH_OPPORTUNITY': return LightbulbIcon;
      case 'PRODUCT_RECOMMENDATION': return CheckCircleIcon;
      case 'COMPETITIVE_GAP': return AlertCircleIcon;
      case 'ACTION_PLAN': return CheckCircleIcon;
      default: return BrainIcon;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const allInsights = [...insights.unread, ...insights.priority].reduce((acc, insight) => {
    if (!acc.find(i => i.id === insight.id)) {
      acc.push(insight);
    }
    return acc;
  }, [] as AIInsight[]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold text-gray-900">AI Growth Coach</h3>
          <p className="text-gray-600">Personalized insights and recommendations</p>
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={() => onGenerateInsight('performance-summary')}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
          >
            <BrainIcon className="w-4 h-4 mr-2 inline" />
            Performance Analysis
          </button>
          
          <button
            onClick={() => onGenerateInsight('growth-opportunity')}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            <LightbulbIcon className="w-4 h-4 mr-2 inline" />
            Growth Opportunities
          </button>
        </div>
      </div>

      {/* Insights Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {allInsights.length > 0 ? (
          allInsights.map((insight) => {
            const Icon = getInsightIcon(insight.insightType);
            
            return (
              <div 
                key={insight.id} 
                className={`bg-white rounded-lg shadow-sm border-l-4 ${
                  insight.priority === 'high' ? 'border-red-500' : 
                  insight.priority === 'medium' ? 'border-yellow-500' : 'border-blue-500'
                } hover:shadow-md transition-shadow cursor-pointer`}
                onClick={() => setSelectedInsight(insight)}
              >
                <div className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center">
                      <Icon className="w-6 h-6 text-gray-600 mr-3" />
                      <div>
                        <h4 className="font-medium text-gray-900">{insight.title}</h4>
                        <span className={`inline-block px-2 py-1 text-xs rounded-full ${getPriorityColor(insight.priority)}`}>
                          {insight.priority} priority
                        </span>
                      </div>
                    </div>
                    
                    {!insight.isRead && (
                      <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                    )}
                  </div>
                  
                  <p className="text-gray-600 mt-3 line-clamp-3">
                    {insight.content.substring(0, 150)}...
                  </p>
                  
                  {insight.actionItems && insight.actionItems.length > 0 && (
                    <div className="mt-4">
                      <p className="text-sm font-medium text-gray-700">Action Items:</p>
                      <ul className="mt-1 text-sm text-gray-600">
                        {insight.actionItems.slice(0, 2).map((item, index) => (
                          <li key={index} className="flex items-center">
                            <CheckCircleIcon className="w-3 h-3 text-green-600 mr-2" />
                            {item.substring(0, 50)}...
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  <div className="mt-4 flex items-center justify-between">
                    <span className="text-xs text-gray-500">
                      {new Date(insight.createdAt).toLocaleDateString()}
                    </span>
                    
                    {!insight.isRead && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          markAsRead(insight.id);
                        }}
                        className="text-xs text-blue-600 hover:text-blue-700"
                      >
                        Mark as read
                      </button>
                    )}
                  </div>
                </div>
              </div>
            );
          })
        ) : (
          <div className="col-span-2 text-center py-12">
            <BrainIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 mb-4">No AI insights available</p>
            <p className="text-sm text-gray-400 mb-6">
              Generate your first AI insight to get personalized recommendations
            </p>
            
            <div className="flex justify-center space-x-4">
              <button
                onClick={() => onGenerateInsight('performance-summary')}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
              >
                Performance Analysis
              </button>
              
              <button
                onClick={() => onGenerateInsight('weekly-action-plan')}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                Weekly Action Plan
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Insight Detail Modal */}
      {selectedInsight && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-bold text-gray-900">{selectedInsight.title}</h3>
                <button
                  onClick={() => setSelectedInsight(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
              
              <div className="prose max-w-none">
                <div className="whitespace-pre-wrap text-gray-700">
                  {selectedInsight.content}
                </div>
                
                {selectedInsight.actionItems && selectedInsight.actionItems.length > 0 && (
                  <div className="mt-6">
                    <h4 className="font-medium text-gray-900 mb-3">Recommended Actions:</h4>
                    <ul className="space-y-2">
                      {selectedInsight.actionItems.map((item, index) => (
                        <li key={index} className="flex items-start">
                          <CheckCircleIcon className="w-5 h-5 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
              
              <div className="mt-6 flex justify-end space-x-3">
                {!selectedInsight.isRead && (
                  <button
                    onClick={() => {
                      markAsRead(selectedInsight.id);
                      setSelectedInsight(null);
                    }}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    Mark as Read
                  </button>
                )}
                
                <button
                  onClick={() => setSelectedInsight(null)}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}