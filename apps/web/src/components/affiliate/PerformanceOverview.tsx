'use client';

import { 
  TrendingUpIcon, 
  TrendingDownIcon,
  DollarSignIcon, 
  ShoppingCartIcon,
  PercentIcon
} from 'lucide-react';

interface PerformanceOverviewProps {
  stats: {
    totalGMV: number;
    totalCommissions: number;
    totalOrders: number;
    averageOrderValue: number;
    conversionRate: number;
    growth: {
      gmv: number;
      commissions: number;
      orders: number;
    };
  };
}

export function PerformanceOverview({ stats }: PerformanceOverviewProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatGrowth = (growth: number) => {
    const isPositive = growth >= 0;
    const Icon = isPositive ? TrendingUpIcon : TrendingDownIcon;
    const colorClass = isPositive ? 'text-green-600' : 'text-red-600';
    
    return (
      <div className={`flex items-center ${colorClass}`}>
        <Icon className="w-4 h-4 mr-1" />
        <span className="text-sm font-medium">
          {Math.abs(growth).toFixed(1)}%
        </span>
      </div>
    );
  };

  const metrics = [
    {
      title: 'Total GMV',
      value: formatCurrency(stats.totalGMV),
      growth: stats.growth.gmv,
      icon: DollarSignIcon,
      iconColor: 'text-green-600',
      bgColor: 'bg-green-100',
      description: 'Last 30 days'
    },
    {
      title: 'Total Commissions',
      value: formatCurrency(stats.totalCommissions),
      growth: stats.growth.commissions,
      icon: DollarSignIcon,
      iconColor: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: 'Your earnings'
    },
    {
      title: 'Total Orders',
      value: stats.totalOrders.toLocaleString(),
      growth: stats.growth.orders,
      icon: ShoppingCartIcon,
      iconColor: 'text-purple-600',
      bgColor: 'bg-purple-100',
      description: 'Orders placed'
    },
    {
      title: 'Average Order Value',
      value: formatCurrency(stats.averageOrderValue),
      growth: null, // AOV doesn't have direct growth comparison
      icon: DollarSignIcon,
      iconColor: 'text-orange-600',
      bgColor: 'bg-orange-100',
      description: 'Per order'
    },
    {
      title: 'Conversion Rate',
      value: `${stats.conversionRate.toFixed(2)}%`,
      growth: null, // Conversion rate growth would need historical data
      icon: PercentIcon,
      iconColor: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
      description: 'Click to purchase'
    },
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900">Performance Overview</h3>
        <p className="text-sm text-gray-600">Your TikTok Shop affiliate performance metrics</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        {metrics.map((metric, index) => (
          <div key={index} className="relative">
            <div className="flex items-center">
              <div className={`flex-shrink-0 ${metric.bgColor} rounded-lg p-3`}>
                <metric.icon className={`h-6 w-6 ${metric.iconColor}`} />
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-500">{metric.title}</p>
                <p className="text-2xl font-bold text-gray-900">{metric.value}</p>
                <p className="text-xs text-gray-600">{metric.description}</p>
              </div>
            </div>
            
            {metric.growth !== null && (
              <div className="mt-2 flex justify-end">
                {formatGrowth(metric.growth)}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Performance Summary */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-900">Overall Performance</p>
            <p className="text-xs text-gray-600">Compared to previous 30 days</p>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-center">
              <p className="text-xs text-gray-500">GMV Growth</p>
              {formatGrowth(stats.growth.gmv)}
            </div>
            <div className="text-center">
              <p className="text-xs text-gray-500">Commission Growth</p>
              {formatGrowth(stats.growth.commissions)}
            </div>
            <div className="text-center">
              <p className="text-xs text-gray-500">Order Growth</p>
              {formatGrowth(stats.growth.orders)}
            </div>
          </div>
        </div>

        {/* Performance Grade */}
        <div className="mt-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Performance Grade</span>
            <span className={`px-3 py-1 rounded-full text-sm font-bold ${getGradeColor(stats.totalCommissions)}`}>
              {getPerformanceGrade(stats.totalCommissions)}
            </span>
          </div>
          <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-500 ${getGradeBarColor(stats.totalCommissions)}`}
              style={{ width: `${getGradePercentage(stats.totalCommissions)}%` }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

function getPerformanceGrade(commissions: number): string {
  if (commissions >= 10000) return 'A+';
  if (commissions >= 5000) return 'A';
  if (commissions >= 2500) return 'B+';
  if (commissions >= 1000) return 'B';
  if (commissions >= 500) return 'C+';
  if (commissions >= 100) return 'C';
  return 'D';
}

function getGradeColor(commissions: number): string {
  if (commissions >= 5000) return 'bg-green-100 text-green-800';
  if (commissions >= 1000) return 'bg-blue-100 text-blue-800';
  if (commissions >= 500) return 'bg-yellow-100 text-yellow-800';
  return 'bg-red-100 text-red-800';
}

function getGradeBarColor(commissions: number): string {
  if (commissions >= 5000) return 'bg-green-600';
  if (commissions >= 1000) return 'bg-blue-600';
  if (commissions >= 500) return 'bg-yellow-600';
  return 'bg-red-600';
}

function getGradePercentage(commissions: number): number {
  if (commissions >= 10000) return 100;
  if (commissions >= 5000) return 90;
  if (commissions >= 2500) return 75;
  if (commissions >= 1000) return 60;
  if (commissions >= 500) return 40;
  if (commissions >= 100) return 25;
  return 10;
}