'use client';

import { ShoppingCartIcon, DollarSignIcon, TrendingUpIcon, ExternalLinkIcon } from 'lucide-react';

interface Product {
  id: string;
  title: string;
  category: string;
  price: number;
  imageUrl?: string;
  affiliateLink?: string;
  trendScore: number;
}

interface ProductPerformance {
  id: string;
  productId: string;
  gmv: number;
  commissions: number;
  orders: number;
  clicks: number;
  conversionRate: number;
}

interface TopProductsSectionProps {
  products: Array<{
    product: Product;
    performance: ProductPerformance;
    rank: number;
  }>;
  userId: string;
}

export function TopProductsSection({ products }: TopProductsSectionProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-2xl font-bold text-gray-900">Top Performing Products</h3>
        <p className="text-gray-600">Your highest revenue generating products</p>
      </div>

      {products.length > 0 ? (
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rank
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Product
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    GMV
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Commissions
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Orders
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Conversion Rate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {products.map((item) => (
                  <tr key={item.product.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold text-white ${
                        item.rank === 1 ? 'bg-yellow-500' :
                        item.rank === 2 ? 'bg-gray-400' :
                        item.rank === 3 ? 'bg-orange-500' : 'bg-blue-500'
                      }`}>
                        {item.rank}
                      </div>
                    </td>
                    
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        {item.product.imageUrl ? (
                          <img 
                            className="h-12 w-12 rounded-lg object-cover mr-4" 
                            src={item.product.imageUrl} 
                            alt={item.product.title}
                          />
                        ) : (
                          <div className="h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center mr-4">
                            <ShoppingCartIcon className="h-6 w-6 text-gray-500" />
                          </div>
                        )}
                        <div>
                          <div className="text-sm font-medium text-gray-900 line-clamp-2 max-w-xs">
                            {item.product.title}
                          </div>
                          <div className="text-sm text-gray-500">
                            {item.product.category}
                          </div>
                          <div className="text-sm text-gray-500">
                            {formatCurrency(item.product.price)}
                          </div>
                        </div>
                      </div>
                    </td>
                    
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {formatCurrency(item.performance.gmv)}
                      </div>
                    </td>
                    
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-green-600">
                        {formatCurrency(item.performance.commissions)}
                      </div>
                    </td>
                    
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {item.performance.orders.toLocaleString()}
                      </div>
                    </td>
                    
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className={`text-sm font-medium ${
                          item.performance.conversionRate >= 3 ? 'text-green-600' :
                          item.performance.conversionRate >= 1.5 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {item.performance.conversionRate.toFixed(2)}%
                        </div>
                      </div>
                    </td>
                    
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      {item.product.affiliateLink && (
                        <a
                          href={item.product.affiliateLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-900 flex items-center"
                        >
                          <ExternalLinkIcon className="w-4 h-4 mr-1" />
                          View Product
                        </a>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm p-12 text-center">
          <ShoppingCartIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 mb-2">No product performance data available</p>
          <p className="text-sm text-gray-400">
            Start promoting products to see your top performers here
          </p>
        </div>
      )}

      {/* Performance Insights */}
      {products.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Performance Insights</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(products.reduce((sum, p) => sum + p.performance.gmv, 0))}
              </div>
              <p className="text-sm text-gray-600">Total GMV from Top Products</p>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {formatCurrency(products.reduce((sum, p) => sum + p.performance.commissions, 0))}
              </div>
              <p className="text-sm text-gray-600">Total Commissions Earned</p>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {(products.reduce((sum, p) => sum + p.performance.conversionRate, 0) / products.length).toFixed(2)}%
              </div>
              <p className="text-sm text-gray-600">Average Conversion Rate</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}