'use client';

import { useState } from 'react';
import { 
  UserIcon, 
  EyeIcon, 
  TrendingUpIcon, 
  BellIcon, 
  RefreshCwIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExternalLinkIcon
} from 'lucide-react';
import { Competitor } from '@xact-data/shared';
import { apiPost } from '../lib/api';

// Using shared Competitor type from @xact-data/shared

interface CompetitorListProps {
  competitors: Competitor[];
  onCompetitorSelect: (competitor: Competitor) => void;
  onCompetitorRemove: (competitorId: string) => void;
  onRefreshCompetitors: () => void;
}

export function CompetitorList({ 
  competitors, 
  onCompetitorSelect, 
  onCompetitorRemove,
  onRefreshCompetitors 
}: CompetitorListProps) {
  const [refreshingIds, setRefreshingIds] = useState<Set<string>>(new Set());

  const handleRefreshCompetitor = async (competitorId: string) => {
    setRefreshingIds(prev => new Set(prev).add(competitorId));
    
    try {
      const response = await apiPost(`api/competitors/${competitorId}/refresh`);
      
      if (response.ok) {
        onRefreshCompetitors();
      }
    } catch (error) {
      console.error('Failed to refresh competitor:', error);
    } finally {
      setRefreshingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(competitorId);
        return newSet;
      });
    }
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const getEngagementColor = (rate: number): string => {
    if (rate >= 5) return 'text-green-600 bg-green-50';
    if (rate >= 3) return 'text-yellow-600 bg-yellow-50';
    if (rate >= 1) return 'text-orange-600 bg-orange-50';
    return 'text-red-600 bg-red-50';
  };

  if (competitors.length === 0) {
    return (
      <div className="text-center py-12">
        <UserIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No competitors</h3>
        <p className="mt-1 text-sm text-gray-500">
          Get started by adding your first competitor to track.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white shadow-sm rounded-lg overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">
          Tracked Competitors ({competitors.length})
        </h3>
      </div>

      <div className="divide-y divide-gray-200">
        {competitors.map((competitor) => (
          <div key={competitor.id} className="p-6 hover:bg-gray-50">
            <div className="flex items-center justify-between">
              {/* Competitor Info */}
              <div className="flex items-center space-x-4">
                <div className="relative">
                  {competitor.creator.profileImageUrl ? (
                    <img
                      src={competitor.creator.profileImageUrl}
                      alt={competitor.creator.username}
                      className="h-12 w-12 rounded-full object-cover"
                    />
                  ) : (
                    <div className="h-12 w-12 rounded-full bg-gray-300 flex items-center justify-center">
                      <UserIcon className="h-6 w-6 text-gray-600" />
                    </div>
                  )}
                  {competitor.creator.isVerified && (
                    <CheckCircleIcon className="absolute -bottom-1 -right-1 h-4 w-4 text-blue-500 bg-white rounded-full" />
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h4 className="text-lg font-medium text-gray-900 truncate">
                      {competitor.creator.displayName || competitor.creator.username}
                    </h4>
                    {competitor.nickname && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {competitor.nickname}
                      </span>
                    )}
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                      competitor.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {competitor.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  <p className="text-sm text-gray-500">@{competitor.creator.username}</p>
                  
                  {/* Stats */}
                  <div className="flex items-center space-x-6 mt-2">
                    <div className="flex items-center text-sm text-gray-600">
                      <UserIcon className="h-4 w-4 mr-1" />
                      {formatNumber(competitor.creator.followerCount || 0)} followers
                    </div>
                    <div className={`flex items-center text-sm px-2 py-1 rounded ${getEngagementColor(competitor.creator.engagementRate || 0)}`}>
                      <TrendingUpIcon className="h-4 w-4 mr-1" />
                      {(competitor.creator.engagementRate || 0).toFixed(1)}% engagement
                    </div>
                    {competitor.creator.totalGMV && competitor.creator.totalGMV > 0 && (
                      <div className="flex items-center text-sm text-gray-600">
                        <span className="font-medium">${formatNumber(competitor.creator.totalGMV || 0)} GMV</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Actions and Metrics */}
              <div className="flex items-center space-x-4">
                {/* Alerts Badge */}
                {competitor.alerts && competitor.alerts.length > 0 && (
                  <div className="flex items-center space-x-1">
                    <BellIcon className="h-4 w-4 text-red-500" />
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      {competitor.alerts.length} new
                    </span>
                  </div>
                )}

                {/* Analyses Count */}
                {competitor.analyses && competitor.analyses.length > 0 && (
                  <div className="flex items-center space-x-1">
                    <span className="text-sm text-gray-500">
                      {competitor.analyses.length} analyses
                    </span>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleRefreshCompetitor(competitor.id)}
                    disabled={refreshingIds.has(competitor.id)}
                    className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                    title="Refresh data"
                  >
                    <RefreshCwIcon 
                      className={`h-4 w-4 ${refreshingIds.has(competitor.id) ? 'animate-spin' : ''}`} 
                    />
                  </button>

                  <button
                    onClick={() => onCompetitorSelect(competitor)}
                    className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <EyeIcon className="h-4 w-4 mr-1" />
                    View
                  </button>

                  <a
                    href={`https://tiktok.com/@${competitor.creator.username}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-2 text-gray-400 hover:text-gray-600"
                    title="View on TikTok"
                  >
                    <ExternalLinkIcon className="h-4 w-4" />
                  </a>

                  <button
                    onClick={() => onCompetitorRemove(competitor.id)}
                    className="p-2 text-gray-400 hover:text-red-600"
                    title="Remove competitor"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}