'use client'

import { signIn, useSession } from 'next-auth/react'
import { useState } from 'react'

interface WhopLoginButtonProps {
  className?: string
  children?: React.ReactNode
}

export function WhopLoginButton({ className = '', children }: WhopLoginButtonProps) {
  const { data: session, status } = useSession()
  const [loading, setLoading] = useState(false)

  const handleSignIn = async () => {
    setLoading(true)
    try {
      await signIn('whop', { callbackUrl: '/dashboard' })
    } catch (error) {
      console.error('Sign in error:', error)
      setLoading(false)
    }
  }

  // Don't show login button if user is already signed in
  if (status === 'authenticated') {
    return null
  }

  return (
    <button
      onClick={handleSignIn}
      disabled={loading || status === 'loading'}
      className={`bg-black text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center ${className}`}
    >
      {loading || status === 'loading' ? (
        <div className="flex items-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          Signing in...
        </div>
      ) : (
        <>
          <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8zm-1-13h2v6h-2V7zm0 8h2v2h-2v-2z"/>
          </svg>
          {children || 'Login with Whop'}
        </>
      )}
    </button>
  )
}