import React from "react";
import { card } from "@tailus/themer";
import { cn } from "../../lib/utils";

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "elevated" | "outlined" | "soft";
}

export const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant = "elevated", children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(card({ variant }), className)}
        data-rounded="default"
        {...props}
      >
        {children}
      </div>
    );
  }
);

Card.displayName = "Card";

export default Card;