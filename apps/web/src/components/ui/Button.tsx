import React from "react";
import { button } from "@tailus/themer";
import { cn } from "../../lib/utils";

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  intent?: "primary" | "secondary" | "success" | "warning" | "danger" | "gray";
  variant?: "solid" | "outlined" | "ghost" | "soft";
  size?: "xs" | "sm" | "md" | "lg" | "xl";
  href?: string;
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, intent = "primary", variant = "solid", size = "md", href, children, ...props }, ref) => {
    if (href) {
      return (
        <a
          className={cn(
            button[variant]({ intent, size }),
            className
          )}
          href={href}
          data-rounded="default"
        >
          {children}
        </a>
      );
    }
    
    return (
      <button
        className={cn(
          button[variant]({ intent, size }),
          className
        )}
        ref={ref}
        {...props}
        data-rounded="default"
      >
        {children}
      </button>
    );
  }
);

Button.displayName = "Button";

export default Button;