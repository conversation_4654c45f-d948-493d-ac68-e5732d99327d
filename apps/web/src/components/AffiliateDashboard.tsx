'use client';

import { useState, useEffect } from 'react';
import { 
  TrendingUpIcon, 
  DollarSignIcon, 
  ShoppingCartIcon, 
  TargetIcon,
  TrophyIcon,
  BrainIcon,
  BarChart3Icon,
  UsersIcon,
  PlusIcon,
  RefreshCwIcon,
  ChevronRightIcon
} from 'lucide-react';
import { apiGet, apiPost } from '../lib/api';
import { PerformanceOverview } from './affiliate/PerformanceOverview';
import { GoalsSection } from './affiliate/GoalsSection';
import { AchievementsSection } from './affiliate/AchievementsSection';
import { AIInsightsSection } from './affiliate/AIInsightsSection';
import { TopProductsSection } from './affiliate/TopProductsSection';
import { CompetitiveBenchmarkSection } from './affiliate/CompetitiveBenchmarkSection';
import { LoadingSpinner } from './LoadingSpinner';
import Button from './ui/Button';
import Card from './ui/Card';
import Badge from './ui/Badge';

interface AffiliateDashboardStats {
  overview: {
    totalGMV: number;
    totalCommissions: number;
    totalOrders: number;
    averageOrderValue: number;
    conversionRate: number;
    growth: {
      gmv: number;
      commissions: number;
      orders: number;
    };
  };
  goals: {
    active: any[];
    completed: number;
    progress: number;
  };
  achievements: {
    recent: any[];
    total: number;
    points: number;
  };
  insights: {
    unread: any[];
    priority: any[];
  };
  topProducts: any[];
  competitiveBenchmarks: any[];
}

export function AffiliateDashboard() {
  const [stats, setStats] = useState<AffiliateDashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'goals' | 'insights' | 'products' | 'competitors'>('overview');

  // Mock user ID - in real app, this would come from auth context
  const userId = 'user-123';

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      const response = await apiGet(`api/affiliate-dashboard/user/${userId}/stats`);
      const data = await response.json();
      
      if (data.success) {
        setStats(data.data);
      } else {
        console.error('Failed to fetch dashboard stats:', data.error);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    setRefreshing(true);
    await fetchDashboardStats();
    setRefreshing(false);
  };

  const generateAIInsight = async (type: string) => {
    try {
      setRefreshing(true);
      const response = await apiPost(`api/affiliate-dashboard/user/${userId}/ai/${type}`, {});
      const data = await response.json();
      
      if (data.success) {
        // Refresh stats to show new insight
        await fetchDashboardStats();
      } else {
        alert(data.error || 'Failed to generate insight');
      }
    } catch (error) {
      console.error('Failed to generate AI insight:', error);
      alert('Failed to generate insight');
    } finally {
      setRefreshing(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Failed to load dashboard data</p>
        <Button 
          onClick={fetchDashboardStats}
          intent="primary"
          className="mt-4"
          data-rounded="default"
        >
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Quick Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h2 className="text-2xl font-bold text-gray-900">Dashboard Overview</h2>
          <Button
            onClick={refreshData}
            disabled={refreshing}
            intent="gray"
            variant="ghost"
            size="sm"
            data-rounded="default"
          >
            <RefreshCwIcon className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            onClick={() => generateAIInsight('performance-summary')}
            disabled={refreshing}
            intent="secondary"
            data-rounded="default"
          >
            <BrainIcon className="w-4 h-4 mr-2" />
            AI Analysis
          </Button>
          
          <Button
            onClick={() => generateAIInsight('weekly-action-plan')}
            disabled={refreshing}
            intent="success"
            data-rounded="default"
          >
            <TargetIcon className="w-4 h-4 mr-2" />
            Action Plan
          </Button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3Icon },
            { id: 'goals', label: 'Goals', icon: TargetIcon },
            { id: 'insights', label: 'AI Insights', icon: BrainIcon },
            { id: 'products', label: 'Top Products', icon: ShoppingCartIcon },
            { id: 'competitors', label: 'Benchmarks', icon: UsersIcon },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="w-4 h-4 mr-2" />
              {tab.label}
              {tab.id === 'insights' && stats.insights.unread.length > 0 && (
                <span className="ml-2 bg-red-100 text-red-600 text-xs rounded-full px-2 py-0.5">
                  {stats.insights.unread.length}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <PerformanceOverview stats={stats.overview} />
            
            {/* Quick Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <TargetIcon className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Active Goals</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.goals.active.length}</p>
                    <p className="text-xs text-green-600">
                      {stats.goals.progress.toFixed(1)}% avg progress
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <TrophyIcon className="h-8 w-8 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Achievements</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.achievements.total}</p>
                    <p className="text-xs text-blue-600">
                      {stats.achievements.points} points
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <BrainIcon className="h-8 w-8 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">AI Insights</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.insights.unread.length}</p>
                    <p className="text-xs text-orange-600">
                      {stats.insights.priority.length} priority
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <UsersIcon className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Benchmarks</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.competitiveBenchmarks.length}</p>
                    <p className="text-xs text-gray-600">vs competitors</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Activity Summary */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Achievements</h3>
                {stats.achievements.recent.length > 0 ? (
                  <div className="space-y-3">
                    {stats.achievements.recent.slice(0, 3).map((achievement, index) => (
                      <div key={index} className="flex items-center p-3 bg-yellow-50 rounded-lg">
                        <TrophyIcon className="w-6 h-6 text-yellow-600 mr-3" />
                        <div>
                          <p className="font-medium text-gray-900">{achievement.title}</p>
                          <p className="text-sm text-gray-600">{achievement.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No achievements yet. Keep pushing towards your goals!</p>
                )}
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Priority Insights</h3>
                {stats.insights.priority.length > 0 ? (
                  <div className="space-y-3">
                    {stats.insights.priority.slice(0, 3).map((insight, index) => (
                      <div key={index} className="flex items-start p-3 bg-purple-50 rounded-lg">
                        <BrainIcon className="w-6 h-6 text-purple-600 mr-3 mt-0.5" />
                        <div className="flex-1">
                          <p className="font-medium text-gray-900">{insight.title}</p>
                          <p className="text-sm text-gray-600 line-clamp-2">
                            {insight.content.substring(0, 100)}...
                          </p>
                        </div>
                        <ChevronRightIcon className="w-4 h-4 text-gray-400" />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <p className="text-gray-500 mb-3">No priority insights available</p>
                    <button
                      onClick={() => generateAIInsight('performance-summary')}
                      className="text-sm text-blue-600 hover:text-blue-700"
                    >
                      Generate AI Analysis
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'goals' && (
          <GoalsSection 
            goals={stats.goals} 
            userId={userId}
            onGoalsUpdate={fetchDashboardStats}
          />
        )}

        {activeTab === 'insights' && (
          <AIInsightsSection 
            insights={stats.insights} 
            userId={userId}
            onGenerateInsight={generateAIInsight}
            onInsightUpdate={fetchDashboardStats}
          />
        )}

        {activeTab === 'products' && (
          <TopProductsSection 
            products={stats.topProducts}
            userId={userId}
          />
        )}

        {activeTab === 'competitors' && (
          <CompetitiveBenchmarkSection 
            benchmarks={stats.competitiveBenchmarks}
            userId={userId}
            onGenerateAnalysis={() => generateAIInsight('competitive-gap-analysis')}
          />
        )}
      </div>
    </div>
  );
}