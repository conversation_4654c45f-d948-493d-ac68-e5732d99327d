'use client';

import { useState, useEffect } from 'react';
import { CompetitorList } from './CompetitorList';
import { AddCompetitorModal } from './AddCompetitorModal';
import { CompetitorProfile } from './CompetitorProfile';
import { CompetitorAnalysis } from './CompetitorAnalysis';
import { PlusIcon } from 'lucide-react';
import { Competitor } from '@xact-data/shared';
import { apiGet, apiPost, apiDelete } from '../lib/api';
import Button from './ui/Button';

// Using shared Competitor type from @xact-data/shared

export function CompetitorDashboard() {
  const [competitors, setCompetitors] = useState<Competitor[]>([]);
  const [selectedCompetitor, setSelectedCompetitor] = useState<Competitor | null>(null);
  const [activeView, setActiveView] = useState<'list' | 'profile' | 'analysis'>('list');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCompetitors();
  }, []);

  const fetchCompetitors = async () => {
    try {
      setLoading(true);
      const response = await apiGet('api/competitors');
      const data = await response.json();
      
      if (data.success) {
        setCompetitors(data.data.competitors || []);
      }
    } catch (error) {
      console.error('Failed to fetch competitors:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddCompetitor = async (username: string, nickname?: string) => {
    try {
      const response = await apiPost('api/competitors', { username, nickname });
      const data = await response.json();
      
      if (data.success) {
        setCompetitors(prev => [data.data, ...prev]);
        setIsAddModalOpen(false);
      } else {
        alert(data.error || 'Failed to add competitor');
      }
    } catch (error) {
      console.error('Failed to add competitor:', error);
      alert('Failed to add competitor');
    }
  };

  const handleRemoveCompetitor = async (competitorId: string) => {
    if (!confirm('Are you sure you want to remove this competitor?')) return;

    try {
      const response = await apiDelete(`api/competitors/${competitorId}`);
      const data = await response.json();
      
      if (data.success) {
        setCompetitors(prev => prev.filter(c => c.id !== competitorId));
        if (selectedCompetitor?.id === competitorId) {
          setSelectedCompetitor(null);
          setActiveView('list');
        }
      } else {
        alert(data.error || 'Failed to remove competitor');
      }
    } catch (error) {
      console.error('Failed to remove competitor:', error);
      alert('Failed to remove competitor');
    }
  };

  const handleCompetitorSelect = (competitor: Competitor) => {
    setSelectedCompetitor(competitor);
    setActiveView('profile');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex space-x-4">
          <button
            onClick={() => setActiveView('list')}
            className={`px-4 py-2 rounded-md font-medium ${
              activeView === 'list'
                ? 'bg-blue-100 text-blue-700 border border-blue-300'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            All Competitors ({competitors.length})
          </button>
          {selectedCompetitor && (
            <>
              <button
                onClick={() => setActiveView('profile')}
                className={`px-4 py-2 rounded-md font-medium ${
                  activeView === 'profile'
                    ? 'bg-blue-100 text-blue-700 border border-blue-300'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Profile
              </button>
              <button
                onClick={() => setActiveView('analysis')}
                className={`px-4 py-2 rounded-md font-medium ${
                  activeView === 'analysis'
                    ? 'bg-blue-100 text-blue-700 border border-blue-300'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Analysis
              </button>
            </>
          )}
        </div>

        <Button
          onClick={() => setIsAddModalOpen(true)}
          intent="primary"
          data-rounded="default"
        >
          <PlusIcon className="w-4 h-4 mr-2" />
          Add Competitor
        </Button>
      </div>

      {/* Main Content */}
      {activeView === 'list' && (
        <CompetitorList
          competitors={competitors}
          onCompetitorSelect={handleCompetitorSelect}
          onCompetitorRemove={handleRemoveCompetitor}
          onRefreshCompetitors={fetchCompetitors}
        />
      )}

      {activeView === 'profile' && selectedCompetitor && (
        <CompetitorProfile
          competitor={selectedCompetitor}
          onBack={() => setActiveView('list')}
        />
      )}

      {activeView === 'analysis' && selectedCompetitor && (
        <CompetitorAnalysis
          competitor={selectedCompetitor}
          onBack={() => setActiveView('profile')}
        />
      )}

      {/* Add Competitor Modal */}
      <AddCompetitorModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onAdd={handleAddCompetitor}
      />
    </div>
  );
}