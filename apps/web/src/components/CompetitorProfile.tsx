'use client';

import { useState, useEffect } from 'react';
import { 
  ArrowLeftIcon, 
  UserIcon, 
  TrendingUpIcon, 
  HeartIcon, 
  PlayIcon,
  CheckCircleIcon,
  ExternalLinkIcon,
  CalendarIcon,
  BarChart3Icon,
  BellIcon,
  RefreshCwIcon
} from 'lucide-react';
import { Competitor } from '@xact-data/shared';
import { apiGet, apiPost } from '../lib/api';

// Using shared Competitor type from @xact-data/shared

interface CompetitorProfileProps {
  competitor: Competitor;
  onBack: () => void;
}

export function CompetitorProfile({ competitor, onBack }: CompetitorProfileProps) {
  const [detailedData, setDetailedData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchDetailedData();
  }, [competitor.id]);

  const fetchDetailedData = async () => {
    try {
      setLoading(true);
      const response = await apiGet(`api/competitors/${competitor.id}`);
      const data = await response.json();
      
      if (data.success) {
        setDetailedData(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch detailed data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      const response = await apiPost(`api/competitors/${competitor.id}/refresh`);
      
      if (response.ok) {
        await fetchDetailedData();
      }
    } catch (error) {
      console.error('Failed to refresh data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatDate = (date: string | Date): string => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getEngagementColor = (rate: number): string => {
    if (rate >= 5) return 'text-green-600';
    if (rate >= 3) return 'text-yellow-600';
    if (rate >= 1) return 'text-orange-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center mb-4">
          <button onClick={onBack} className="mr-4 p-2 hover:bg-gray-100 rounded-md">
            <ArrowLeftIcon className="h-5 w-5" />
          </button>
          <div className="animate-pulse flex-1">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          </div>
        </div>
        <div className="animate-pulse space-y-4">
          <div className="flex items-center space-x-4">
            <div className="h-16 w-16 bg-gray-200 rounded-full"></div>
            <div className="space-y-2 flex-1">
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/3"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const creator = detailedData?.creator || competitor.creator;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <button onClick={onBack} className="mr-4 p-2 hover:bg-gray-100 rounded-md">
              <ArrowLeftIcon className="h-5 w-5" />
            </button>
            <h1 className="text-2xl font-bold text-gray-900">
              {creator.displayName || creator.username}
            </h1>
            {competitor.nickname && (
              <span className="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                {competitor.nickname}
              </span>
            )}
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <RefreshCwIcon className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              {refreshing ? 'Refreshing...' : 'Refresh Data'}
            </button>

            <a
              href={`https://tiktok.com/@${creator.username}`}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <ExternalLinkIcon className="h-4 w-4 mr-2" />
              View on TikTok
            </a>
          </div>
        </div>

        {/* Profile Info */}
        <div className="flex items-start space-x-6">
          <div className="relative">
            {creator.profileImageUrl ? (
              <img
                src={creator.profileImageUrl}
                alt={creator.username}
                className="h-20 w-20 rounded-full object-cover"
              />
            ) : (
              <div className="h-20 w-20 rounded-full bg-gray-300 flex items-center justify-center">
                <UserIcon className="h-10 w-10 text-gray-600" />
              </div>
            )}
            {creator.isVerified && (
              <CheckCircleIcon className="absolute -bottom-1 -right-1 h-6 w-6 text-blue-500 bg-white rounded-full" />
            )}
          </div>

          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h2 className="text-xl font-semibold text-gray-900">@{creator.username}</h2>
              {creator.isVerified && (
                <CheckCircleIcon className="h-5 w-5 text-blue-500" />
              )}
            </div>
            
            {creator.bio && (
              <p className="text-gray-600 mb-4">{creator.bio}</p>
            )}

            <div className="flex items-center text-sm text-gray-500 space-x-4">
              <div className="flex items-center">
                <CalendarIcon className="h-4 w-4 mr-1" />
                Added {formatDate(competitor.createdAt)}
              </div>
              {detailedData?.creator?.updatedAt && (
                <div className="flex items-center">
                  <RefreshCwIcon className="h-4 w-4 mr-1" />
                  Updated {formatDate(detailedData.creator.updatedAt)}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <UserIcon className="h-8 w-8 text-blue-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Followers</p>
              <p className="text-2xl font-semibold text-gray-900">
                {formatNumber(creator.followerCount)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <HeartIcon className="h-8 w-8 text-red-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Likes</p>
              <p className="text-2xl font-semibold text-gray-900">
                {formatNumber(creator.likesCount)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <PlayIcon className="h-8 w-8 text-purple-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Videos</p>
              <p className="text-2xl font-semibold text-gray-900">
                {formatNumber(creator.videoCount)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUpIcon className={`h-8 w-8 ${getEngagementColor(creator.engagementRate)}`} />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Engagement Rate</p>
              <p className={`text-2xl font-semibold ${getEngagementColor(creator.engagementRate)}`}>
                {creator.engagementRate.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Metrics</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Following</span>
              <span className="font-medium">{formatNumber(creator.followingCount)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Likes per Video</span>
              <span className="font-medium">
                {creator.videoCount > 0 
                  ? formatNumber(Math.round(creator.likesCount / creator.videoCount))
                  : '0'
                }
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Followers per Video</span>
              <span className="font-medium">
                {creator.videoCount > 0 
                  ? formatNumber(Math.round(creator.followerCount / creator.videoCount))
                  : '0'
                }
              </span>
            </div>
            {creator.totalGMV && creator.totalGMV > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total GMV</span>
                <span className="font-medium text-green-600">
                  ${formatNumber(creator.totalGMV)}
                </span>
              </div>
            )}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Tracking Status</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Status</span>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                competitor.isActive 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {competitor.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Unread Alerts</span>
              <span className="font-medium flex items-center">
                <BellIcon className="h-4 w-4 mr-1 text-red-500" />
                {competitor.alerts?.length || 0}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Analyses</span>
              <span className="font-medium flex items-center">
                <BarChart3Icon className="h-4 w-4 mr-1 text-blue-500" />
                {competitor.analyses?.length || 0}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Videos */}
      {detailedData?.creator?.videos && detailedData.creator.videos.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Videos</h3>
          <div className="space-y-4">
            {detailedData.creator.videos.slice(0, 5).map((video: any) => (
              <div key={video.id} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {video.title || 'Untitled Video'}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {formatDate(video.publishedAt)}
                  </p>
                </div>
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  <div className="flex items-center">
                    <PlayIcon className="h-4 w-4 mr-1" />
                    {formatNumber(video.viewCount)}
                  </div>
                  <div className="flex items-center">
                    <HeartIcon className="h-4 w-4 mr-1" />
                    {formatNumber(video.likeCount)}
                  </div>
                  {video.engagementRate && (
                    <div className={`flex items-center ${getEngagementColor(video.engagementRate)}`}>
                      <TrendingUpIcon className="h-4 w-4 mr-1" />
                      {video.engagementRate.toFixed(1)}%
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}