'use client';

import { useState } from 'react';
import { SearchIcon, FilterIcon, XIcon } from 'lucide-react';

interface FilterProps {
  category: string;
  minTrendScore: number;
  sortBy: 'trendScore' | 'soldIn24h' | 'estimatedGMV' | 'commissionRate';
  sortOrder: 'asc' | 'desc';
  page: number;
  limit: number;
}

interface ProductFiltersProps {
  filters: FilterProps;
  onFilterChange: (filters: Partial<FilterProps>) => void;
}

const categories = [
  'Electronics',
  'Fashion',
  'Beauty',
  'Home & Garden',
  'Sports & Outdoors',
  'Health & Wellness',
  'Toys & Games',
  'Books & Media',
  'Food & Beverages',
  'Automotive',
  'Pet Supplies',
  'Other'
];

export function ProductFilters({ filters, onFilterChange }: ProductFiltersProps) {
  const [showFilters, setShowFilters] = useState(false);

  const handleCategoryChange = (category: string) => {
    onFilterChange({ category: category === filters.category ? '' : category });
  };

  const handleTrendScoreChange = (minTrendScore: number) => {
    onFilterChange({ minTrendScore });
  };

  const handleSortChange = (sortBy: FilterProps['sortBy'], sortOrder: FilterProps['sortOrder']) => {
    onFilterChange({ sortBy, sortOrder });
  };

  const clearFilters = () => {
    onFilterChange({
      category: '',
      minTrendScore: 0,
      sortBy: 'trendScore',
      sortOrder: 'desc',
    });
  };

  const hasActiveFilters = filters.category || filters.minTrendScore > 0;

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
      {/* Filter Toggle */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <FilterIcon className="w-4 h-4 mr-2" />
            Filters
            {hasActiveFilters && (
              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Active
              </span>
            )}
          </button>
          
          {hasActiveFilters && (
            <button
              onClick={clearFilters}
              className="inline-flex items-center px-3 py-1.5 text-sm text-gray-500 hover:text-gray-700"
            >
              <XIcon className="w-4 h-4 mr-1" />
              Clear Filters
            </button>
          )}
        </div>

        {/* Sort Controls */}
        <div className="flex items-center space-x-4">
          <label className="text-sm font-medium text-gray-700">Sort by:</label>
          <select
            value={filters.sortBy}
            onChange={(e) => handleSortChange(e.target.value as FilterProps['sortBy'], filters.sortOrder)}
            className="border border-gray-300 rounded-md px-3 py-1.5 text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="trendScore">Trend Score</option>
            <option value="soldIn24h">24h Sales</option>
            <option value="estimatedGMV">Estimated GMV</option>
            <option value="commissionRate">Commission Rate</option>
          </select>
          
          <select
            value={filters.sortOrder}
            onChange={(e) => handleSortChange(filters.sortBy, e.target.value as FilterProps['sortOrder'])}
            className="border border-gray-300 rounded-md px-3 py-1.5 text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="desc">Highest First</option>
            <option value="asc">Lowest First</option>
          </select>
        </div>
      </div>

      {/* Expandable Filters */}
      {showFilters && (
        <div className="border-t border-gray-200 pt-4 space-y-6">
          {/* Category Filter */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-3 block">Category</label>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => handleCategoryChange(category)}
                  className={`inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                    filters.category === category
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          {/* Trend Score Filter */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-3 block">
              Minimum Trend Score: {filters.minTrendScore}
            </label>
            <div className="flex items-center space-x-4">
              <input
                type="range"
                min="0"
                max="100"
                step="5"
                value={filters.minTrendScore}
                onChange={(e) => handleTrendScoreChange(Number(e.target.value))}
                className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex space-x-2">
                {[0, 50, 70, 85].map((score) => (
                  <button
                    key={score}
                    onClick={() => handleTrendScoreChange(score)}
                    className={`px-2 py-1 text-xs rounded ${
                      filters.minTrendScore === score
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    {score === 0 ? 'All' : `${score}+`}
                  </button>
                ))}
              </div>
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Low</span>
              <span>Medium</span>
              <span>High</span>
              <span>Trending</span>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Filter Legend</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
              <div>
                <div className="flex items-center mb-1">
                  <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                  <span className="text-gray-600">Low (0-40)</span>
                </div>
              </div>
              <div>
                <div className="flex items-center mb-1">
                  <div className="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                  <span className="text-gray-600">Medium (40-60)</span>
                </div>
              </div>
              <div>
                <div className="flex items-center mb-1">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                  <span className="text-gray-600">High (60-80)</span>
                </div>
              </div>
              <div>
                <div className="flex items-center mb-1">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                  <span className="text-gray-600">Trending (80+)</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Add custom CSS for the range slider
const styles = `
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3B82F6;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3B82F6;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}