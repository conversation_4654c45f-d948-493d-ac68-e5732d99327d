'use client';

import { ReactNode, useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { usePathname } from 'next/navigation';
import { 
  BellIcon, 
  SearchIcon, 
  MenuIcon,
  ChevronRightIcon 
} from 'lucide-react';
import Button from '../ui/Button';
import { AppSidebar } from './AppSidebar';
import { WhopLoginButton } from '../auth/WhopLoginButton';
import { NoSSR } from '../NoSSR';

interface AppLayoutProps {
  children: ReactNode;
}

const pathNames: Record<string, string> = {
  '/dashboard': 'Dashboard',
  '/products': 'Products',
  '/alerts': 'Alerts',
  '/analytics': 'Analytics',
  '/competitors': 'Competitors',
  '/settings': 'Settings',
};

export function AppLayout({ children }: AppLayoutProps) {
  const pathname = usePathname();

  return (
    <NoSSR 
      fallback={
        <div className="flex h-screen items-center justify-center bg-gray-50">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>
      }
    >
      <AppLayoutContent pathname={pathname}>
        {children}
      </AppLayoutContent>
    </NoSSR>
  );
}

function AppLayoutContent({ children, pathname }: { children: ReactNode; pathname: string }) {
  const { data: session, status } = useSession();

  // Bypass authentication in development mode
  const isDevelopment = process.env.NODE_ENV === 'development';

  // Mock user for development mode
  const mockUser = {
    name: 'Demo User',
    email: '<EMAIL>',
    image: null
  };

  const currentUser = isDevelopment && !session ? mockUser : session?.user;

  if (status === 'loading' && !isDevelopment) {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // In development, skip authentication check and show the app directly
  // In production, require authentication
  if (!session && !isDevelopment) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="w-full max-w-md space-y-8">
          <div className="text-center">
            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary-500 mb-4">
              <span className="text-2xl font-bold text-white">X</span>
            </div>
            <h2 className="text-3xl font-bold text-gray-900">Welcome to Xact Data</h2>
            <p className="mt-2 text-gray-600">Sign in to access your Creator OS dashboard</p>
          </div>
          <div className="flex justify-center">
            <WhopLoginButton />
          </div>
        </div>
      </div>
    );
  }

  const currentPageName = pathNames[pathname] || 'Dashboard';

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <AppSidebar />

      {/* Main Content Area - Layered Design */}
      <div className="flex flex-1 flex-col overflow-hidden">
        {/* Content Container with Rounded Corners and Shadow */}
        <div className="flex flex-1 flex-col bg-white m-4 rounded-2xl shadow-lg overflow-hidden">
          {/* Top Navigation */}
          <header className="flex h-16 items-center justify-between border-b border-gray-200 bg-white px-6">
            <div className="flex items-center gap-4">
              {/* Mobile menu button */}
              <Button
                intent="gray"
                variant="ghost"
                size="sm"
                className="md:hidden rounded-xl"
                data-rounded="large"
              >
                <MenuIcon className="h-5 w-5" />
              </Button>

              {/* Breadcrumbs */}
              <div className="flex items-center gap-2 text-sm">
                <span className="text-gray-500">Dashboard</span>
                {pathname !== '/dashboard' && (
                  <>
                    <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                    <span className="font-medium text-gray-900">{currentPageName}</span>
                  </>
                )}
              </div>
            </div>

            <div className="flex items-center gap-4">
              {/* Global Search */}
              <div className="relative hidden md:block">
                <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search products, competitors..."
                  className="w-64 rounded-xl border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
                  data-rounded="large"
                />
              </div>

              {/* Notifications */}
              <Button
                intent="gray"
                variant="ghost"
                size="sm"
                className="relative rounded-xl"
                data-rounded="large"
              >
                <BellIcon className="h-5 w-5" />
                <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-red-500"></span>
              </Button>

              {/* User Avatar */}
              {currentUser && (
                <div className="flex items-center gap-3">
                  {currentUser.image ? (
                    <img
                      src={currentUser.image}
                      alt={currentUser.name || 'User'}
                      className="h-8 w-8 rounded-full"
                    />
                  ) : (
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-100">
                      <span className="text-sm font-medium text-primary-700">
                        {currentUser.name?.charAt(0) || currentUser.email?.charAt(0)}
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>
          </header>

          {/* Page Content */}
          <main className="flex-1 overflow-auto">
            <div className="h-full p-6">
              {children}
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}