'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useSession, signOut } from 'next-auth/react';
import { 
  HomeIcon,
  ShoppingBagIcon,
  BellIcon,
  BarChart3Icon,
  UsersIcon,
  SettingsIcon,
  LogOutIcon,
  SearchIcon,
  PlusIcon
} from 'lucide-react';
import Button from '../ui/Button';
import { cn } from '../../lib/utils';

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
  { name: 'Products', href: '/products', icon: ShoppingBagIcon },
  { name: 'Alerts', href: '/alerts', icon: BellIcon },
  { name: 'Analytics', href: '/analytics', icon: BarChart3Icon },
  { name: 'Competitors', href: '/competitors', icon: UsersIcon },
  { name: 'Settings', href: '/settings', icon: SettingsIcon },
];

export function AppSidebar() {
  const pathname = usePathname();
  const { data: session } = useSession();

  // Mock user for development mode
  const isDevelopment = process.env.NODE_ENV === 'development';
  const mockUser = {
    name: 'Demo User',
    email: '<EMAIL>',
    image: null
  };

  const currentUser = isDevelopment && !session ? mockUser : session?.user;

  return (
    <div className="flex h-full w-64 flex-col bg-white border-r border-gray-200">
      {/* Logo Section */}
      <div className="flex items-center gap-3 px-6 py-4 border-b border-gray-200">
        <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary-500">
          <span className="text-sm font-bold text-white">X</span>
        </div>
        <div className="flex flex-col">
          <span className="text-lg font-semibold text-gray-900">Xact Data</span>
          <span className="text-xs text-gray-500">Creator OS</span>
        </div>
      </div>

      {/* Search Bar */}
      <div className="px-4 py-4">
        <div className="relative">
          <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search..."
            className="w-full rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
            data-rounded="default"
          />
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 px-4">
        {navigation.map((item) => {
          const isActive = pathname === item.href;
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors',
                isActive
                  ? 'bg-primary-50 text-primary-700'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              )}
              data-rounded="default"
            >
              <item.icon className="h-5 w-5" />
              {item.name}
            </Link>
          );
        })}
      </nav>

      {/* Quick Actions */}
      <div className="px-4 py-4 border-t border-gray-200">
        <Button intent="primary" size="sm" className="w-full justify-center" data-rounded="default">
          <PlusIcon className="h-4 w-4 mr-2" />
          New Alert
        </Button>
      </div>

      {/* User Profile */}
      {currentUser && (
        <div className="border-t border-gray-200 p-4">
          <div className="flex items-center gap-3 mb-3">
            {currentUser.image ? (
              <img
                src={currentUser.image}
                alt={currentUser.name || 'User'}
                className="h-8 w-8 rounded-full"
              />
            ) : (
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-100">
                <span className="text-sm font-medium text-primary-700">
                  {currentUser.name?.charAt(0) || currentUser.email?.charAt(0)}
                </span>
              </div>
            )}
            <div className="flex flex-col">
              <span className="text-sm font-medium text-gray-900">
                {currentUser.name || currentUser.email}
              </span>
              <span className="text-xs text-gray-500">Creator</span>
            </div>
          </div>
          <Button
            intent="gray"
            variant="ghost"
            size="sm"
            className="w-full justify-start"
            onClick={() => {
              if (isDevelopment && !session) {
                // In development mode with mock user, just reload the page
                window.location.reload();
              } else {
                signOut({ callbackUrl: '/' });
              }
            }}
            data-rounded="default"
          >
            <LogOutIcon className="h-4 w-4 mr-2" />
            {isDevelopment && !session ? 'Refresh' : 'Sign out'}
          </Button>
        </div>
      )}
    </div>
  );
}