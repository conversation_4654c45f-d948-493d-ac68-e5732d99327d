/** @type {import('tailwindcss').Config} */
const { palettes } = require('@tailus/themer');

module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    './node_modules/@tailus/themer/dist/**/*.{js,ts}',
    './node_modules/@tailus/themer/dist/components/**/*.{js,ts}',
  ],
  darkMode: 'class', // Explicitly set to 'class' to prevent automatic dark mode
  theme: {
    extend: {
      borderRadius: {
        'DEFAULT': '0.75rem', // 12px - increased default radius
        'lg': '1rem', // 16px
        'xl': '1.25rem', // 20px
        '2xl': '1.5rem', // 24px
      },
      colors: {
        ...palettes.trust,
        primary: {
          50: '#e6f3ff',
          100: '#b3daff',
          200: '#80c1ff',
          300: '#4da8ff',
          400: '#1a8fff',
          500: '#007FFF', // Main primary color
          600: '#0066cc',
          700: '#004d99',
          800: '#003366',
          900: '#001a33',
          DEFAULT: '#007FFF',
        },
      },
    },
  },
  plugins: [],
}