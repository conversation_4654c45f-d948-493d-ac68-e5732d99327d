import { Router } from 'express';
import { z } from 'zod';
import { createApiResponse } from '@xact-data/shared';
import { asyncHandler } from '../middleware/error-handler';
import { db } from '@xact-data/database';

const router = Router();

// Schema for user creation/update
const WhopUserSchema = z.object({
  whopId: z.string(),
  email: z.string().email(),
  name: z.string().optional(),
  image: z.string().url().optional(),
});

/**
 * Create or update user from Whop OAuth
 */
router.post('/whop/user', asyncHandler(async (req, res) => {
  const userData = WhopUserSchema.parse(req.body);

  // Check if user already exists by whopId
  let user = await db.user.findUnique({
    where: { whopId: userData.whopId }
  });

  if (user) {
    // Update existing user
    user = await db.user.update({
      where: { whopId: userData.whopId },
      data: {
        email: userData.email,
        name: userData.name,
        image: userData.image,
        updatedAt: new Date(),
      },
    });
  } else {
    // Check if user exists with same email
    const existingUser = await db.user.findUnique({
      where: { email: userData.email }
    });

    if (existingUser) {
      // Link whopId to existing user
      user = await db.user.update({
        where: { email: userData.email },
        data: {
          whopId: userData.whopId,
          name: userData.name || existingUser.name,
          image: userData.image || existingUser.image,
          updatedAt: new Date(),
        },
      });
    } else {
      // Create new user
      user = await db.user.create({
        data: {
          whopId: userData.whopId,
          email: userData.email,
          name: userData.name,
          image: userData.image,
        },
      });
    }
  }

  res.json(createApiResponse(true, user, 'User created/updated successfully'));
}));

/**
 * Get user by whopId
 */
router.get('/whop/user/:whopId', asyncHandler(async (req, res) => {
  const { whopId } = req.params;

  const user = await db.user.findUnique({
    where: { whopId },
    include: {
      analytics: true,
      alerts: true,
      affiliateGoals: true,
      achievements: true,
    },
  });

  if (!user) {
    return res.status(404).json(
      createApiResponse(false, null, 'User not found')
    );
  }

  res.json(createApiResponse(true, user, 'User retrieved successfully'));
}));

export default router;