import { Router } from 'express';
import {
  getDashboardStats,
  getGoals,
  createGoal,
  updateGoal,
  deleteGoal,
  getAchievements,
  getAchievementProgress,
  checkAchievements,
  getInsights,
  createInsight,
  markInsightRead,
  getProductPerformance,
  getCompetitiveBenchmarks,
  generatePerformanceSummary,
  generateGrowthOpportunity,
  generateProductRecommendations,
  generateCompetitiveGapAnalysis,
  generateWeeklyActionPlan,
} from '../controllers/affiliate-dashboard';

const router = Router();

// Dashboard Stats
router.get('/user/:userId/stats', getDashboardStats);

// Goals Management
router.get('/user/:userId/goals', getGoals);
router.post('/user/:userId/goals', createGoal);
router.put('/user/:userId/goals/:goalId', updateGoal);
router.delete('/user/:userId/goals/:goalId', deleteGoal);

// Achievements
router.get('/user/:userId/achievements', getAchievements);
router.get('/user/:userId/achievements/progress', getAchievementProgress);
router.post('/user/:userId/achievements/check', checkAchievements);

// AI Insights
router.get('/user/:userId/insights', getInsights);
router.post('/user/:userId/insights', createInsight);
router.put('/user/:userId/insights/:insightId/read', markInsightRead);

// Product Performance
router.get('/user/:userId/product-performance', getProductPerformance);

// Competitive Benchmarks
router.get('/user/:userId/competitive-benchmarks', getCompetitiveBenchmarks);

// AI Growth Coach
router.post('/user/:userId/ai/performance-summary', generatePerformanceSummary);
router.post('/user/:userId/ai/growth-opportunity', generateGrowthOpportunity);
router.post('/user/:userId/ai/product-recommendations', generateProductRecommendations);
router.post('/user/:userId/ai/competitive-gap-analysis', generateCompetitiveGapAnalysis);
router.post('/user/:userId/ai/weekly-action-plan', generateWeeklyActionPlan);

export default router;