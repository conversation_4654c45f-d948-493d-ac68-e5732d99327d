import { Request, Response } from 'express';
import { prisma } from '@xact-data/database';
import { createApiResponse } from '@xact-data/shared';
import { asyncHandler } from '../middleware/error-handler';
import { z } from 'zod';
import { AIGrowthCoachService, AchievementService } from '@xact-data/shared';
import type { 
  AffiliateDashboardStats, 
  AffiliateGoal, 
  Achievement, 
  AIInsight,
  CompetitiveBenchmark,
  ProductPerformance 
} from '@xact-data/shared';

// Validation schemas
const CreateGoalSchema = z.object({
  goalType: z.enum(['MONTHLY_GMV', 'MONTHLY_COMMISSIONS', 'CONVERSION_RATE', 'PRODUCT_SALES', 'CUSTOM']),
  title: z.string().min(1).max(100),
  description: z.string().optional(),
  targetValue: z.number().min(0),
  targetDate: z.string().datetime().optional(),
});

const UpdateGoalSchema = z.object({
  title: z.string().min(1).max(100).optional(),
  description: z.string().optional(),
  targetValue: z.number().min(0).optional(),
  targetDate: z.string().datetime().optional(),
  currentValue: z.number().min(0).optional(),
  isCompleted: z.boolean().optional(),
});

const CreateInsightSchema = z.object({
  insightType: z.enum(['PERFORMANCE_SUMMARY', 'GROWTH_OPPORTUNITY', 'PRODUCT_RECOMMENDATION', 'COMPETITIVE_GAP', 'ACTION_PLAN', 'TREND_ALERT']),
  title: z.string().min(1).max(200),
  content: z.string().min(1),
  priority: z.enum(['low', 'medium', 'high']).default('medium'),
  actionItems: z.array(z.string()).optional(),
  metadata: z.record(z.unknown()).optional(),
  validUntil: z.string().datetime().optional(),
});

// Helper function for successful API responses
const successResponse = <T>(data: T) => createApiResponse(true, data);
const errorResponse = (error: string) => createApiResponse(false, undefined, error);

// Get comprehensive affiliate dashboard stats
export const getDashboardStats = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;

  // Get date ranges
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);

  // Parallel queries for dashboard data
  const [
    currentAnalytics,
    previousAnalytics,
    activeGoals,
    completedGoals,
    recentAchievements,
    totalAchievements,
    unreadInsights,
    priorityInsights,
    topProductPerformance,
    competitiveBenchmarks,
  ] = await Promise.all([
    // Current period analytics
    prisma.analytics.findMany({
      where: {
        userId,
        date: { gte: thirtyDaysAgo },
      },
      orderBy: { date: 'desc' },
    }),
    
    // Previous period analytics for growth calculation
    prisma.analytics.findMany({
      where: {
        userId,
        date: {
          gte: sixtyDaysAgo,
          lt: thirtyDaysAgo,
        },
      },
    }),

    // Active goals
    prisma.affiliateGoal.findMany({
      where: {
        userId,
        isCompleted: false,
      },
      orderBy: { createdAt: 'desc' },
      take: 5,
    }),

    // Completed goals count
    prisma.affiliateGoal.count({
      where: {
        userId,
        isCompleted: true,
      },
    }),

    // Recent achievements
    prisma.achievement.findMany({
      where: { userId },
      orderBy: { unlockedAt: 'desc' },
      take: 5,
    }),

    // Total achievements count
    prisma.achievement.count({
      where: { userId },
    }),

    // Unread insights
    prisma.aIInsight.findMany({
      where: {
        userId,
        isRead: false,
      },
      orderBy: { createdAt: 'desc' },
      take: 10,
    }),

    // Priority insights
    prisma.aIInsight.findMany({
      where: {
        userId,
        priority: 'high',
        isRead: false,
      },
      orderBy: { createdAt: 'desc' },
      take: 5,
    }),

    // Top performing products
    prisma.productPerformance.findMany({
      where: {
        userId,
        date: { gte: thirtyDaysAgo },
      },
      include: {
        product: true,
      },
      orderBy: { gmv: 'desc' },
      take: 10,
    }),

    // Competitive benchmarks
    prisma.competitiveBenchmark.findMany({
      where: {
        userId,
        date: { gte: thirtyDaysAgo },
      },
      include: {
        competitor: true,
      },
      orderBy: { date: 'desc' },
      take: 5,
    }),
  ]);

  // Calculate current period totals
  const currentTotals = currentAnalytics.reduce(
    (acc, curr) => ({
      gmv: acc.gmv + curr.gmv,
      commissions: acc.commissions + curr.commissions,
      orders: acc.orders + curr.orders,
    }),
    { gmv: 0, commissions: 0, orders: 0 }
  );

  // Calculate previous period totals
  const previousTotals = previousAnalytics.reduce(
    (acc, curr) => ({
      gmv: acc.gmv + curr.gmv,
      commissions: acc.commissions + curr.commissions,
      orders: acc.orders + curr.orders,
    }),
    { gmv: 0, commissions: 0, orders: 0 }
  );

  // Calculate growth percentages
  const growth = {
    gmv: previousTotals.gmv > 0 
      ? ((currentTotals.gmv - previousTotals.gmv) / previousTotals.gmv) * 100 
      : 0,
    commissions: previousTotals.commissions > 0 
      ? ((currentTotals.commissions - previousTotals.commissions) / previousTotals.commissions) * 100 
      : 0,
    orders: previousTotals.orders > 0 
      ? ((currentTotals.orders - previousTotals.orders) / previousTotals.orders) * 100 
      : 0,
  };

  // Calculate average order value and conversion rate
  const averageOrderValue = currentTotals.orders > 0 ? currentTotals.gmv / currentTotals.orders : 0;
  const totalClicks = topProductPerformance.reduce((sum, p) => sum + p.clicks, 0);
  const conversionRate = totalClicks > 0 ? (currentTotals.orders / totalClicks) * 100 : 0;

  // Calculate goal progress
  const goalProgress = activeGoals.length > 0 
    ? activeGoals.reduce((sum, goal) => sum + (goal.currentValue / goal.targetValue * 100), 0) / activeGoals.length
    : 0;

  // Calculate achievement points (simple scoring system)
  const achievementPoints = totalAchievements * 10;

  // Process top products
  const topProducts = topProductPerformance
    .reduce((acc, curr) => {
      const existing = acc.find(p => p.productId === curr.productId);
      if (existing) {
        existing.gmv += curr.gmv;
        existing.commissions += curr.commissions;
        existing.orders += curr.orders;
        existing.clicks += curr.clicks;
      } else {
        acc.push(curr);
      }
      return acc;
    }, [] as typeof topProductPerformance)
    .sort((a, b) => b.gmv - a.gmv)
    .slice(0, 5)
    .map((perf, index) => ({
      product: perf.product,
      performance: {
        id: perf.id,
        userId: perf.userId,
        productId: perf.productId,
        date: perf.date,
        gmv: perf.gmv,
        commissions: perf.commissions,
        orders: perf.orders,
        clicks: perf.clicks,
        conversionRate: perf.conversionRate,
      },
      rank: index + 1,
    }));

  // Process competitive benchmarks
  const processedBenchmarks = competitiveBenchmarks.map(benchmark => ({
    competitor: benchmark.competitor,
    benchmark: {
      id: benchmark.id,
      userId: benchmark.userId,
      competitorId: benchmark.competitorId,
      metric: benchmark.metric,
      userValue: benchmark.userValue,
      competitorValue: benchmark.competitorValue,
      period: benchmark.period,
      date: benchmark.date,
      createdAt: benchmark.createdAt,
    },
    performance: benchmark.userValue > benchmark.competitorValue 
      ? 'ahead' as const
      : benchmark.userValue < benchmark.competitorValue 
        ? 'behind' as const 
        : 'tied' as const,
  }));

  const dashboardStats: AffiliateDashboardStats = {
    overview: {
      totalGMV: currentTotals.gmv,
      totalCommissions: currentTotals.commissions,
      totalOrders: currentTotals.orders,
      averageOrderValue,
      conversionRate,
      growth,
    },
    goals: {
      active: activeGoals as AffiliateGoal[],
      completed: completedGoals,
      progress: goalProgress,
    },
    achievements: {
      recent: recentAchievements as Achievement[],
      total: totalAchievements,
      points: achievementPoints,
    },
    insights: {
      unread: unreadInsights as AIInsight[],
      priority: priorityInsights as AIInsight[],
    },
    topProducts,
    competitiveBenchmarks: processedBenchmarks,
  };

  res.json(successResponse(dashboardStats));
});

// Goals Management
export const getGoals = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { completed } = req.query;

  const goals = await prisma.affiliateGoal.findMany({
    where: {
      userId,
      ...(completed !== undefined && { isCompleted: completed === 'true' }),
    },
    orderBy: { createdAt: 'desc' },
  });

  res.json(successResponse({ goals }));
});

export const createGoal = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const validatedData = CreateGoalSchema.parse(req.body);

  const goal = await prisma.affiliateGoal.create({
    data: {
      userId,
      goalType: validatedData.goalType,
      title: validatedData.title,
      description: validatedData.description,
      targetValue: validatedData.targetValue,
      targetDate: validatedData.targetDate ? new Date(validatedData.targetDate) : null,
    },
  });

  res.status(201).json(successResponse(goal));
});

export const updateGoal = asyncHandler(async (req: Request, res: Response) => {
  const { userId, goalId } = req.params;
  const validatedData = UpdateGoalSchema.parse(req.body);

  const goal = await prisma.affiliateGoal.updateMany({
    where: {
      id: goalId,
      userId, // Ensure user can only update their own goals
    },
    data: {
      ...validatedData,
      targetDate: validatedData.targetDate ? new Date(validatedData.targetDate) : undefined,
      completedAt: validatedData.isCompleted ? new Date() : undefined,
    },
  });

  if (goal.count === 0) {
    return res.status(404).json(errorResponse('Goal not found'));
  }

  const updatedGoal = await prisma.affiliateGoal.findUnique({
    where: { id: goalId },
  });

  res.json(successResponse(updatedGoal));
});

export const deleteGoal = asyncHandler(async (req: Request, res: Response) => {
  const { userId, goalId } = req.params;

  const goal = await prisma.affiliateGoal.deleteMany({
    where: {
      id: goalId,
      userId, // Ensure user can only delete their own goals
    },
  });

  if (goal.count === 0) {
    return res.status(404).json(errorResponse('Goal not found'));
  }

  res.json(successResponse({ deleted: true }));
});

// Achievements
export const getAchievements = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;

  const achievements = await prisma.achievement.findMany({
    where: { userId },
    orderBy: { unlockedAt: 'desc' },
  });

  res.json(successResponse({ achievements }));
});

export const getAchievementProgress = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const achievementService = new AchievementService();

  const progress = await achievementService.getAchievementProgress(userId);
  res.json(successResponse(progress));
});

export const checkAchievements = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const achievementService = new AchievementService();

  await achievementService.checkAndAwardAchievements(userId);
  
  // Return updated achievements
  const achievements = await prisma.achievement.findMany({
    where: { userId },
    orderBy: { unlockedAt: 'desc' },
  });

  res.json(successResponse({ achievements, checked: true }));
});

// AI Insights
export const getInsights = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { unread, priority } = req.query;

  const insights = await prisma.aIInsight.findMany({
    where: {
      userId,
      ...(unread === 'true' && { isRead: false }),
      ...(priority && { priority: priority as string }),
    },
    orderBy: { createdAt: 'desc' },
  });

  res.json(successResponse({ insights }));
});

export const createInsight = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const validatedData = CreateInsightSchema.parse(req.body);

  const insight = await prisma.aIInsight.create({
    data: {
      userId,
      insightType: validatedData.insightType,
      title: validatedData.title,
      content: validatedData.content,
      priority: validatedData.priority,
      actionItems: validatedData.actionItems,
      metadata: validatedData.metadata as any,
      validUntil: validatedData.validUntil ? new Date(validatedData.validUntil) : null,
    },
  });

  res.status(201).json(successResponse(insight));
});

export const markInsightRead = asyncHandler(async (req: Request, res: Response) => {
  const { userId, insightId } = req.params;

  const insight = await prisma.aIInsight.updateMany({
    where: {
      id: insightId,
      userId, // Ensure user can only update their own insights
    },
    data: {
      isRead: true,
    },
  });

  if (insight.count === 0) {
    return res.status(404).json(errorResponse('Insight not found'));
  }

  res.json(successResponse({ marked: true }));
});

// Product Performance
export const getProductPerformance = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { days = '30', productId } = req.query;

  const daysAgo = new Date();
  daysAgo.setDate(daysAgo.getDate() - parseInt(days as string));

  const performance = await prisma.productPerformance.findMany({
    where: {
      userId,
      date: { gte: daysAgo },
      ...(productId && { productId: productId as string }),
    },
    include: {
      product: true,
    },
    orderBy: { date: 'desc' },
  });

  res.json(successResponse({ performance }));
});

// Competitive Benchmarks
export const getCompetitiveBenchmarks = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { days = '30', competitorId, metric } = req.query;

  const daysAgo = new Date();
  daysAgo.setDate(daysAgo.getDate() - parseInt(days as string));

  const benchmarks = await prisma.competitiveBenchmark.findMany({
    where: {
      userId,
      date: { gte: daysAgo },
      ...(competitorId && { competitorId: competitorId as string }),
      ...(metric && { metric: metric as string }),
    },
    include: {
      competitor: true,
    },
    orderBy: { date: 'desc' },
  });

  res.json(successResponse({ benchmarks }));
});

// AI Growth Coach Endpoints
export const generatePerformanceSummary = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const growthCoach = new AIGrowthCoachService();

  const insight = await growthCoach.generatePerformanceSummary(userId);
  
  // Save insight to database
  const savedInsight = await prisma.aIInsight.create({
    data: {
      userId,
      insightType: insight.insightType,
      title: insight.title,
      content: insight.content,
      priority: insight.priority,
      actionItems: insight.actionItems,
      metadata: insight.metadata,
      validUntil: insight.validUntil,
    },
  });

  res.json(successResponse(savedInsight));
});

export const generateGrowthOpportunity = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const growthCoach = new AIGrowthCoachService();

  const insight = await growthCoach.generateGrowthOpportunity(userId);
  
  // Save insight to database
  const savedInsight = await prisma.aIInsight.create({
    data: {
      userId,
      insightType: insight.insightType,
      title: insight.title,
      content: insight.content,
      priority: insight.priority,
      actionItems: insight.actionItems,
      metadata: insight.metadata,
      validUntil: insight.validUntil,
    },
  });

  res.json(successResponse(savedInsight));
});

export const generateProductRecommendations = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const growthCoach = new AIGrowthCoachService();

  const insight = await growthCoach.generateProductRecommendations(userId);
  
  // Save insight to database
  const savedInsight = await prisma.aIInsight.create({
    data: {
      userId,
      insightType: insight.insightType,
      title: insight.title,
      content: insight.content,
      priority: insight.priority,
      actionItems: insight.actionItems,
      metadata: insight.metadata,
      validUntil: insight.validUntil,
    },
  });

  res.json(successResponse(savedInsight));
});

export const generateCompetitiveGapAnalysis = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { competitorId } = req.body;
  const growthCoach = new AIGrowthCoachService();

  const insight = await growthCoach.generateCompetitiveGapAnalysis(userId, competitorId);
  
  // Save insight to database
  const savedInsight = await prisma.aIInsight.create({
    data: {
      userId,
      insightType: insight.insightType,
      title: insight.title,
      content: insight.content,
      priority: insight.priority,
      actionItems: insight.actionItems,
      metadata: insight.metadata,
      validUntil: insight.validUntil,
    },
  });

  res.json(successResponse(savedInsight));
});

export const generateWeeklyActionPlan = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const growthCoach = new AIGrowthCoachService();

  const insight = await growthCoach.generateWeeklyActionPlan(userId);
  
  // Save insight to database
  const savedInsight = await prisma.aIInsight.create({
    data: {
      userId,
      insightType: insight.insightType,
      title: insight.title,
      content: insight.content,
      priority: insight.priority,
      actionItems: insight.actionItems,
      metadata: insight.metadata,
      validUntil: insight.validUntil,
    },
  });

  res.json(successResponse(savedInsight));
});