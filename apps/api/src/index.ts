import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

import { errorHandler } from './middleware/error-handler';
import { validateEnvVar } from '@xact-data/shared';
import { testDatabaseConnection, disconnectDatabase } from '@xact-data/database';

// Import routes
import productRoutes from './routes/products';
import alertRoutes from './routes/alerts';
import creatorRoutes from './routes/creators';
import analyticsRoutes from './routes/analytics';
import competitorRoutes from './routes/competitors';
import affiliateDashboardRoutes from './routes/affiliate-dashboard';
import authRoutes from './routes/auth';

const app = express();
const PORT = process.env.PORT || 8080;

// Trust proxy for Railway deployment
app.set('trust proxy', true);

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
});

// Middleware
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(limiter);
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV 
  });
});

// API routes
app.use('/api/products', productRoutes);
app.use('/api/alerts', alertRoutes);
app.use('/api/creators', creatorRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/competitors', competitorRoutes);
app.use('/api/affiliate-dashboard', affiliateDashboardRoutes);
app.use('/api/auth', authRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ 
    success: false, 
    error: 'Route not found',
    path: req.originalUrl 
  });
});

// Error handler
app.use(errorHandler);

// Start server with database connection test
async function startServer() {
  try {
    // Check for DATABASE_URL first
    if (!process.env.DATABASE_URL) {
      console.error('❌ CRITICAL: DATABASE_URL environment variable is not set!');
      console.error('');
      console.error('🚨 Railway Configuration Required:');
      console.error('1. Ensure PostgreSQL service is added to your Railway project');
      console.error('2. In your app service Variables, add:');
      console.error('   DATABASE_URL = ${{Postgres.DATABASE_URL}}');
      console.error('   (Replace "Postgres" with your actual PostgreSQL service name)');
      console.error('');
      console.error('📚 For detailed setup instructions, see: RAILWAY_SETUP.md');
      console.error('🔧 Run debug script: ./scripts/railway-debug.sh');
      process.exit(1);
    }

    // Test database connection
    console.log('🔍 Testing database connection...');
    console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🗄️ Database URL configured: ${process.env.DATABASE_URL ? 'Yes' : 'No'}`);
    
    const dbConnected = await testDatabaseConnection();
    
    if (!dbConnected) {
      console.error('❌ Failed to connect to database. Server not starting.');
      console.error('');
      console.error('💡 Troubleshooting steps:');
      console.error('1. Verify PostgreSQL service is running in Railway dashboard');
      console.error('2. Check that DATABASE_URL references the correct service');
      console.error('3. Ensure services are in the same Railway project');
      console.error('4. Run: ./scripts/railway-debug.sh for detailed diagnostics');
      process.exit(1);
    }

    app.listen(PORT, () => {
      console.log(`🚀 API server running on port ${PORT}`);
      console.log(`📊 Environment: ${process.env.NODE_ENV}`);
      console.log(`🔗 Health check: http://localhost:${PORT}/health`);
      console.log(`💾 Database: Connected`);
    });
  } catch (error) {
    console.error('❌ Server startup failed:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  await disconnectDatabase();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  await disconnectDatabase();
  process.exit(0);
});

startServer();