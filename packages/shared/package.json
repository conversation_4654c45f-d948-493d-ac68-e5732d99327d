{"name": "@xact-data/shared", "version": "1.0.0", "description": "Shared utilities and types for Xact Data", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts"}, "dependencies": {"zod": "^3.22.0", "@xact-data/database": "workspace:*", "@xact-data/ai-wrapper": "workspace:*"}, "devDependencies": {"typescript": "^5.1.0", "@types/node": "^20.0.0"}}