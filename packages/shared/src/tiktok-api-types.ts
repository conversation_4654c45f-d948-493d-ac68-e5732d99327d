// TikTok Shop API Response Types
export interface TikTokApiResponse {
  code: number;
  error_code: string;
  msg: string;
  msg_detail: string;
  data: TikTokProduct[];
}

export interface TikTokProduct {
  id: string;
  rank: number;
  category1?: string;
  category2?: string;
  category3?: string;
  country_code: string;
  first_time: number;
  last_time: string;
  last_time_stamp: number;
  title: string;
  sold_count: number;
  sold_count_str: string;
  total_sales: string;
  total_sales_usd: string;
  commission_rate?: string;
  price: string;
  price_usd: string;
  stock: number;
  images_privatization: string[];
  currency: string;
  min_price: number;
  max_price: number;
  skus: TikTokSku[];
  sale_props: TikTokSaleProp[];
  seller_id: string;
  user_id: string;
  seller_name: string;
  seller_avatar_privatization: string;
  related_videos?: number;
  related_author?: number;
  ship_from?: string;
  free_shipping: boolean;
  biz_type: boolean;
  shop: TikTokShop;
  product_rating: number;
  comment_rate?: number;
  product_status: boolean;
  week_sold_count: number;
  week_sold_count_str: string;
  week_sales: string;
  week_sales_usd: string;
  monitor: boolean;
  export: boolean;
  like: boolean;
}

export interface TikTokSku {
  sku_id: string;
  sku_sale_props: string[];
  stock: number;
  price: number;
  currency: string;
  price_str: string;
  price_usd_str: string;
}

export interface TikTokSaleProp {
  prop_name: string;
  has_image: boolean;
  sale_prop_values: TikTokSalePropValue[];
}

export interface TikTokSalePropValue {
  prop_value: string;
  image: string;
  image_privatization?: string;
}

export interface TikTokShop {
  shop_id: string;
  shop_name: string;
  shop_logo_privatization: string;
  shop_status: boolean;
}

// Configuration for TikTok API
export interface TikTokApiConfig {
  rapidApiKey: string;
  rapidApiHost: string;
  baseUrl: string;
}

// Query parameters for TikTok API
export interface TikTokApiQuery {
  keyword?: string;
  country_code: string;
  end_product_rating?: number;
  start_product_rating?: number;
  limit: number;
  page: number;
}

// TikTok User API Types
export interface TikTokUserInfoResponse {
  extra: {
    fatal_item_ids: string[];
    logid: string;
    now: number;
  };
  log_pb: {
    impr_id: string;
  };
  shareMeta: {
    desc: string;
    title: string;
  };
  statusCode: number;
  status_code: number;
  status_msg: string;
  userInfo: {
    stats: {
      diggCount: number;
      followerCount: number;
      followingCount: number;
      friendCount: number;
      heart: number;
      heartCount: number;
      videoCount: number;
    };
    user: {
      avatarLarger: string;
      avatarMedium: string;
      avatarThumb: string;
      bioLink?: {
        link: string;
        risk: number;
      };
      canExpPlaylist: boolean;
      commentSetting: number;
      commerceUserInfo: {
        commerceUser: boolean;
      };
      downloadSetting: number;
      duetSetting: number;
      followingVisibility: number;
      ftc: boolean;
      id: string;
      isADVirtual: boolean;
      isEmbedBanned: boolean;
      nickNameModifyTime: number;
      nickname: string;
      openFavorite: boolean;
      privateAccount: boolean;
      profileEmbedPermission: number;
      profileTab: {
        showMusicTab: boolean;
        showPlayListTab: boolean;
      };
      relation: number;
      secUid: string;
      secret: boolean;
      signature: string;
      stitchSetting: number;
      ttSeller: boolean;
      uniqueId: string;
      verified: boolean;
    };
  };
}

// TikTok Posts API Types
export interface TikTokPostsResponse {
  data: {
    cursor: string;
    extra: {
      fatal_item_ids: string[];
      logid: string;
      now: number;
    };
    hasMore: boolean;
    itemList: TikTokPostItem[];
  };
}

export interface TikTokPostItem {
  AIGCDescription: string;
  CategoryType: number;
  HasPromoteEntry: number;
  author: {
    avatarLarger: string;
    avatarMedium: string;
    avatarThumb: string;
    commentSetting: number;
    downloadSetting: number;
    duetSetting: number;
    ftc: boolean;
    id: string;
    isADVirtual: boolean;
    isEmbedBanned: boolean;
    nickname: string;
    openFavorite: boolean;
    privateAccount: boolean;
    relation: number;
    secUid: string;
    secret: boolean;
    signature: string;
    stitchSetting: number;
    uniqueId: string;
    verified: boolean;
  };
  authorStats: {
    diggCount: number;
    followerCount: number;
    followingCount: number;
    friendCount: number;
    heart: number;
    heartCount: number;
    videoCount: number;
  };
  backendSourceEventTracking: string;
  challenges: Array<{
    coverLarger: string;
    coverMedium: string;
    coverThumb: string;
    desc: string;
    id: string;
    profileLarger: string;
    profileMedium: string;
    profileThumb: string;
    title: string;
  }>;
  collected: boolean;
  contents: Array<{
    desc: string;
    textExtra: Array<{
      awemeId: string;
      end: number;
      hashtagName: string;
      isCommerce: boolean;
      start: number;
      subType: number;
      type: number;
    }>;
  }>;
  createTime: number;
  desc: string;
  digged: boolean;
  diversificationId: number;
  duetDisplay: number;
  duetEnabled: boolean;
  forFriend: boolean;
  id: string;
  itemCommentStatus: number;
  item_control: {
    can_repost: boolean;
  };
  music: {
    album: string;
    authorName: string;
    coverLarge: string;
    coverMedium: string;
    coverThumb: string;
    duration: number;
    id: string;
    original: boolean;
    playUrl: string;
    title: string;
  };
  officalItem: boolean;
  originalItem: boolean;
  privateItem: boolean;
  secret: boolean;
  shareEnabled: boolean;
  stats: {
    collectCount: number;
    commentCount: number;
    diggCount: number;
    playCount: number;
    shareCount: number;
  };
  statsV2: {
    collectCount: string;
    commentCount: string;
    diggCount: string;
    playCount: string;
    repostCount: string;
    shareCount: string;
  };
  stitchDisplay: number;
  stitchEnabled: boolean;
  textExtra: Array<{
    awemeId: string;
    end: number;
    hashtagName: string;
    isCommerce: boolean;
    start: number;
    subType: number;
    type: number;
  }>;
  textLanguage: string;
  textTranslatable: boolean;
  video: {
    VQScore: string;
    bitrate: number;
    bitrateInfo: Array<{
      Bitrate: number;
      CodecType: string;
      GearName: string;
      MVMAF: string;
      PlayAddr: {
        DataSize: number;
        FileCs: string;
        FileHash: string;
        Height: number;
        Uri: string;
        UrlKey: string;
        UrlList: string[];
        Width: number;
      };
      QualityType: number;
    }>;
  };
}