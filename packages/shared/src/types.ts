import { z } from 'zod';

// User types
export const UserSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  tiktokShopId: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type User = z.infer<typeof UserSchema>;

// Product types
export const ProductSchema = z.object({
  id: z.string(),
  title: z.string(),
  category: z.string(),
  commissionRate: z.number().min(0).max(100),
  soldIn24h: z.number().int().min(0),
  creatorsCarrying: z.number().int().min(0),
  estimatedGMV: z.number().min(0),
  trendScore: z.number().min(0).max(100),
  affiliateLink: z.string().url().optional(),
  imageUrl: z.string().url().optional(),
  price: z.number().min(0),
  createdAt: z.date(),
  updatedAt: z.date(),
  
  // TikTok API specific fields
  tiktokProductId: z.string().optional(),
  rank: z.number().int().optional(),
  category1: z.string().optional(),
  category2: z.string().optional(),
  category3: z.string().optional(),
  countryCode: z.string().optional(),
  soldCount: z.number().int().optional(),
  totalSales: z.string().optional(),
  weekSoldCount: z.number().int().optional(),
  weekSales: z.string().optional(),
  productRating: z.number().optional(),
  sellerId: z.string().optional(),
  sellerName: z.string().optional(),
  shopId: z.string().optional(),
  shopName: z.string().optional(),
  stock: z.number().int().optional(),
  relatedVideos: z.number().int().optional(),
  relatedAuthors: <AUTHORS>
  freeShipping: z.boolean().optional(),
  lastTimeStamp: z.bigint().optional(),
});

export type Product = z.infer<typeof ProductSchema>;

// Alert types
export const AlertSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  productId: z.string(),
  threshold: z.number().min(0).max(100),
  isActive: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type Alert = z.infer<typeof AlertSchema>;

// Alert Fire types
export const AlertFireSchema = z.object({
  id: z.string().uuid(),
  alertId: z.string().uuid(),
  productId: z.string(),
  trendScore: z.number(),
  firedAt: z.date(),
  notified: z.boolean().default(false),
});

export type AlertFire = z.infer<typeof AlertFireSchema>;

// Creator types
export const CreatorSchema = z.object({
  id: z.string(),
  username: z.string(),
  displayName: z.string().optional(),
  bio: z.string().optional(),
  followerCount: z.number().int().min(0),
  followingCount: z.number().int().min(0),
  likesCount: z.number().int().min(0),
  videoCount: z.number().int().min(0),
  averageViews: z.number().int().min(0),
  engagementRate: z.number().min(0).max(100),
  totalGMV: z.number().min(0),
  profileImageUrl: z.string().url().optional(),
  isVerified: z.boolean(),
  tiktokUserId: z.string().optional(),
  secUid: z.string().optional(),
  region: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type Creator = z.infer<typeof CreatorSchema>;

// Analytics types
export const AnalyticsSchema = z.object({
  userId: z.string().uuid(),
  date: z.date(),
  gmv: z.number().min(0),
  commissions: z.number().min(0),
  orders: z.number().int().min(0),
  aov: z.number().min(0),
  conversionRate: z.number().min(0).max(100),
});

export type Analytics = z.infer<typeof AnalyticsSchema>;

// API Response types
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.unknown().optional(),
  error: z.string().optional(),
  message: z.string().optional(),
});

export type ApiResponse<T = unknown> = {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
};

// Competitor Tracking types
export const CompetitorTrackingSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  creatorId: z.string(),
  nickname: z.string().optional(),
  isActive: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type CompetitorTracking = z.infer<typeof CompetitorTrackingSchema>;

// Extended Competitor type that includes creator relationship and related data
export interface Competitor {
  id: string;
  userId: string;
  creatorId: string;
  nickname?: string;
  isActive: boolean;
  createdAt: string | Date;
  updatedAt: string | Date;
  creator: Creator;
  alerts?: CompetitorAlert[];
  analyses?: CompetitorAnalysis[];
}

export const CompetitorAlertTypeSchema = z.enum([
  'NEW_VIDEO',
  'VIRAL_POST', 
  'LIVESTREAM',
  'PRODUCT_LAUNCH',
  'FOLLOWER_MILESTONE',
  'ENGAGEMENT_SPIKE',
  'SALES_MILESTONE',
]);

export type CompetitorAlertType = z.infer<typeof CompetitorAlertTypeSchema>;

export const CompetitorAlertSchema = z.object({
  id: z.string().uuid(),
  competitorTrackingId: z.string().uuid(),
  alertType: CompetitorAlertTypeSchema,
  title: z.string(),
  description: z.string(),
  metadata: z.record(z.unknown()).optional(),
  isRead: z.boolean(),
  createdAt: z.date(),
});

export type CompetitorAlert = z.infer<typeof CompetitorAlertSchema>;

export const CompetitorAnalysisTypeSchema = z.enum([
  'STRENGTHS_WEAKNESSES',
  'CONTENT_ANALYSIS',
  'PERFORMANCE_COMPARISON',
  'GAP_OPPORTUNITIES',
  'GROWTH_STRATEGY',
]);

export type CompetitorAnalysisType = z.infer<typeof CompetitorAnalysisTypeSchema>;

export const CompetitorAnalysisSchema = z.object({
  id: z.string().uuid(),
  competitorTrackingId: z.string().uuid(),
  analysisType: CompetitorAnalysisTypeSchema,
  title: z.string(),
  content: z.string(),
  strengths: z.array(z.string()).optional(),
  weaknesses: z.array(z.string()).optional(),
  opportunities: z.array(z.string()).optional(),
  actionItems: z.array(z.string()).optional(),
  score: z.number().min(0).max(100).optional(),
  createdAt: z.date(),
});

export type CompetitorAnalysis = z.infer<typeof CompetitorAnalysisSchema>;

export const CreatorVideoSchema = z.object({
  id: z.string(),
  creatorId: z.string(),
  title: z.string().optional(),
  description: z.string().optional(),
  viewCount: z.number().int().min(0),
  likeCount: z.number().int().min(0),
  shareCount: z.number().int().min(0),
  commentCount: z.number().int().min(0),
  playTime: z.number().int().optional(),
  publishedAt: z.date(),
  thumbnailUrl: z.string().url().optional(),
  videoUrl: z.string().url().optional(),
  hashtags: z.array(z.string()).optional(),
  mentionedProducts: z.array(z.string()).optional(),
  engagementRate: z.number().min(0).max(100).optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type CreatorVideo = z.infer<typeof CreatorVideoSchema>;

// Affiliate Dashboard Types

// Goal Types
export const GoalTypeSchema = z.enum([
  'MONTHLY_GMV',
  'MONTHLY_COMMISSIONS',
  'CONVERSION_RATE',
  'PRODUCT_SALES',
  'CUSTOM',
]);

export type GoalType = z.infer<typeof GoalTypeSchema>;

export const AffiliateGoalSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  goalType: GoalTypeSchema,
  title: z.string(),
  description: z.string().optional(),
  targetValue: z.number().min(0),
  currentValue: z.number().min(0),
  targetDate: z.date().optional(),
  isCompleted: z.boolean(),
  completedAt: z.date().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type AffiliateGoal = z.infer<typeof AffiliateGoalSchema>;

// Achievement Types
export const AchievementTypeSchema = z.enum([
  'FIRST_SALE',
  'MONTHLY_MILESTONE',
  'STREAK',
  'PRODUCT_MASTER',
  'GROWTH_SPURT',
  'CONVERSION_KING',
]);

export type AchievementType = z.infer<typeof AchievementTypeSchema>;

export const AchievementSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  type: AchievementTypeSchema,
  title: z.string(),
  description: z.string(),
  badgeIcon: z.string(),
  badgeColor: z.string(),
  unlockedAt: z.date(),
  value: z.number().optional(),
});

export type Achievement = z.infer<typeof AchievementSchema>;

// Competitive Benchmark Types
export const CompetitiveBenchmarkSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  competitorId: z.string(),
  metric: z.string(),
  userValue: z.number(),
  competitorValue: z.number(),
  period: z.string(),
  date: z.date(),
  createdAt: z.date(),
});

export type CompetitiveBenchmark = z.infer<typeof CompetitiveBenchmarkSchema>;

// AI Insight Types
export const InsightTypeSchema = z.enum([
  'PERFORMANCE_SUMMARY',
  'GROWTH_OPPORTUNITY',
  'PRODUCT_RECOMMENDATION',
  'COMPETITIVE_GAP',
  'ACTION_PLAN',
  'TREND_ALERT',
]);

export type InsightType = z.infer<typeof InsightTypeSchema>;

export const AIInsightSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  insightType: InsightTypeSchema,
  title: z.string(),
  content: z.string(),
  priority: z.string(),
  actionItems: z.array(z.string()).optional(),
  metadata: z.record(z.unknown()).optional(),
  isRead: z.boolean(),
  validUntil: z.date().optional(),
  createdAt: z.date(),
});

export type AIInsight = z.infer<typeof AIInsightSchema>;

// Product Performance Types
export const ProductPerformanceSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  productId: z.string(),
  date: z.date(),
  gmv: z.number().min(0),
  commissions: z.number().min(0),
  orders: z.number().int().min(0),
  clicks: z.number().int().min(0),
  conversionRate: z.number().min(0).max(100),
});

export type ProductPerformance = z.infer<typeof ProductPerformanceSchema>;

// Dashboard Summary Types
export interface AffiliateDashboardStats {
  overview: {
    totalGMV: number;
    totalCommissions: number;
    totalOrders: number;
    averageOrderValue: number;
    conversionRate: number;
    growth: {
      gmv: number;
      commissions: number;
      orders: number;
    };
  };
  goals: {
    active: AffiliateGoal[];
    completed: number;
    progress: number; // Overall goal completion percentage
  };
  achievements: {
    recent: Achievement[];
    total: number;
    points: number; // Gamification points
  };
  insights: {
    unread: AIInsight[];
    priority: AIInsight[];
  };
  topProducts: Array<{
    product: Product;
    performance: ProductPerformance;
    rank: number;
  }>;
  competitiveBenchmarks: Array<{
    competitor: Creator;
    benchmark: CompetitiveBenchmark;
    performance: 'ahead' | 'behind' | 'tied';
  }>;
}