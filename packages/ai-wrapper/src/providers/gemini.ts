import { GoogleGener<PERSON><PERSON><PERSON>, HarmCategory, HarmBlockThreshold } from '@google/generative-ai';
import { ChatMessage, AIResponse } from '../types';

export class GeminiProvider {
  private client: GoogleGenerativeAI;
  private model: string;
  private maxTokens?: number;
  private temperature?: number;

  constructor(
    apiKey: string,
    model = 'gemini-1.5-flash',
    maxTokens?: number,
    temperature?: number
  ) {
    this.client = new GoogleGenerativeAI(apiKey);
    this.model = model;
    this.maxTokens = maxTokens;
    this.temperature = temperature;
  }

  async chat(messages: ChatMessage[]): Promise<AIResponse> {
    const model = this.client.getGenerativeModel({ 
      model: this.model,
      generationConfig: {
        maxOutputTokens: this.maxTokens,
        temperature: this.temperature,
      },
      safetySettings: [
        {
          category: HarmCategory.HARM_CATEGORY_HARASSMENT,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
      ],
    });

    // Convert messages to Gemini format
    const systemMessage = messages.find(msg => msg.role === 'system');
    const chatMessages = messages.filter(msg => msg.role !== 'system');

    // Build the conversation history
    const history = chatMessages.slice(0, -1).map(msg => ({
      role: msg.role === 'assistant' ? 'model' : 'user',
      parts: [{ text: msg.content }],
    }));

    // Get the latest user message
    const latestMessage = chatMessages[chatMessages.length - 1];
    
    // Combine system message with user message if system exists
    let prompt = latestMessage?.content || '';
    if (systemMessage) {
      prompt = `${systemMessage.content}\n\n${prompt}`;
    }

    try {
      const chat = model.startChat({ history });
      const result = await chat.sendMessage(prompt);
      const response = result.response;
      const text = response.text();

      // Extract usage information if available
      const usage = undefined; // Usage metadata may not be available in all Gemini responses

      return {
        content: text,
        usage,
      };
    } catch (error) {
      console.error('Gemini API error:', error);
      throw new Error(`Gemini API request failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async generateInsight(prompt: string): Promise<string> {
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: 'You are a strategic TikTok Shop growth consultant helping creators dominate their competition. Write insights in a conversational, human tone as if you\'re personally coaching them. Focus on actionable strategies to outperform competitors and maximize revenue. Avoid robotic language, technical jargon, or generic advice. Be specific, direct, and motivational.',
      },
      {
        role: 'user',
        content: prompt,
      },
    ];

    const response = await this.chat(messages);
    return response.content;
  }

  async generateCompetitorAnalysis(
    creatorData: string,
    competitorData: string
  ): Promise<string> {
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: 'You are a competitive intelligence expert helping creators crush their competition. Your job is to identify exactly how the user can outperform their competitor and steal market share. Write in a confident, strategic tone like a business mentor. Focus on what the USER needs to do to win, not how to improve the competitor. Be specific about tactics to gain competitive advantage.',
      },
      {
        role: 'user',
        content: `Help me dominate this competitor. Here's my data vs theirs:

MY PROFILE:
${creatorData}

COMPETITOR I WANT TO BEAT:
${competitorData}

Show me exactly how to:
1. Exploit their weaknesses to gain followers
2. Steal their audience with better content
3. Outrank them on products they're promoting
4. Capture market share they're missing
5. Build on my strengths to surpass them

Give me a battle plan to win.`,
      },
    ];

    const response = await this.chat(messages);
    return response.content;
  }
}