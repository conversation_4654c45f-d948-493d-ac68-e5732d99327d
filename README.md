# Xact Data - TikTok Shop Creator OS

The ultimate operating system for TikTok Shop creators, providing winning product discovery, real-time alerts, performance analytics, and competitor tracking.

## 🚀 Features

### Winning Products
- **Dynamic Table View**: Title, category, commission %, 24h sold, creators carrying, estimated GMV, trend score bar, "Get Link"
- **Real-Time Product Alerts**: Instant notifications when a product starts trending
- **Performance Analytics**: Personalized recommendations on which products maximize the creator's revenue

### Live Alerts
- **Threshold Slider**: Users set a trend-score threshold and surface products exceeding that score
- **Automated Alerts**: Cron worker runs hourly; results stored in AlertFire for user notifications

### Affiliate Dashboard
- **Personal Sales Tracking**: Direct TikTok Shop integration to sync commissions and payouts
- **AI Growth Coach**: Personalized insights, gap analysis, daily/weekly action plans
- **Goal Mapping & Gamification**: Visual roadmap toward $1K → $50K months, achievement badges for milestones
- **Competitive Benchmarking**: Side-by-side comparisons vs competitors

### Competitor Tracking
- **Creator Profiles & Libraries**: Full dossiers for TikTok Shop creators
- **Top Creators by Product**: Leaderboards by sales, virality, and conversions
- **AI Strengths & Weaknesses Reports**: Hooks, pacing, angles, weak CTAs, and gap opportunities
- **Competitive Gap Playbooks**: AI generates step-by-step playbooks to outperform a competitor or product

## 🏗️ Tech Stack

- **Frontend**: Next.js 14 (React + Tailwind CSS, TypeScript)
- **Backend API**: Node.js + Express (TypeScript), Zod validation, dotenv
- **Database**: Supabase PostgreSQL + Prisma ORM
- **Jobs**: node-cron for trend computation and alert firing
- **Auth**: OAuth 2.0 with TikTok Shop Open Platform (planned)
- **AI**: Provider-agnostic wrapper (Gemini/OpenAI/Anthropic) for ranking explanations & insights
- **Tooling**: pnpm workspaces, ESLint/Prettier

## 📁 Monorepo Structure

```
├── apps/
│   ├── web/          # Next.js frontend application
│   ├── api/          # Express.js backend API
│   └── worker/       # Background jobs with node-cron
├── packages/
│   ├── shared/       # Shared utilities and types
│   ├── database/     # Prisma database layer
│   └── ai-wrapper/   # Provider-agnostic AI client
├── infra/            # Infrastructure configuration
└── prisma/           # Database schema and migrations
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- pnpm 8+
- Supabase account and project (replaces local PostgreSQL)

### Installation

1. **Clone and install dependencies**:
   ```bash
   git clone <repository-url>
   cd xact-data
   pnpm install
   ```

2. **Set up environment variables** (pre-configured):
   ```bash
   # Environment files are already configured with Supabase
   # Run the setup script to create .env files automatically
   ./scripts/setup.sh
   ```

3. **Set up the Supabase database**:
   ```bash
   cd packages/database
   pnpm db:generate  # Generate Prisma client
   pnpm db:push      # Push schema to Supabase
   pnpm db:seed      # Seed with initial data
   ```

4. **Start development**:
   ```bash
   # Start all applications with one command
   pnpm dev
   ```

   Applications will be available at:
   - **Web**: http://localhost:3000
   - **API**: http://localhost:8080
   - **Worker**: Background process

## 🔧 Development

### Available Scripts

- `pnpm dev` - Start all applications in development mode
- `pnpm build` - Build all applications
- `pnpm lint` - Lint all packages
- `pnpm format` - Format code with Prettier
- `pnpm type-check` - Run TypeScript type checking

### Supabase Database Operations

```bash
cd packages/database

# Generate Prisma client
pnpm db:generate

# Push schema changes to Supabase
pnpm db:push

# Create and run migrations (for production)
pnpm db:migrate

# Open Prisma Studio (connects to Supabase)
pnpm db:studio

# Seed the Supabase database
pnpm db:seed
```

## 🌐 API Endpoints

### Products
- `GET /api/products` - List products with filtering and pagination
- `GET /api/products/trending` - Get trending products
- `GET /api/products/:id` - Get product details
- `POST /api/products` - Create product
- `PUT /api/products/:id` - Update product
- `DELETE /api/products/:id` - Delete product

### Alerts
- `GET /api/alerts/user/:userId` - Get user alerts
- `POST /api/alerts` - Create alert
- `PUT /api/alerts/:id` - Update alert
- `DELETE /api/alerts/:id` - Delete alert
- `GET /api/alerts/fires/user/:userId` - Get alert fires

### Creators
- `GET /api/creators` - List creators
- `GET /api/creators/:id` - Get creator details
- `GET /api/creators/product/:productId/top` - Top creators by product
- `GET /api/creators/:id1/compare/:id2` - Compare creators

### Analytics
- `GET /api/analytics/user/:userId` - Get user analytics
- `GET /api/analytics/user/:userId/dashboard` - Dashboard stats

## 🤖 AI Integration

The AI wrapper supports Gemini, OpenAI, and Anthropic providers:

```typescript
import { AIClient } from '@xact-data/ai-wrapper';

const aiClient = new AIClient({
  provider: 'gemini', // or 'openai', 'anthropic'
  apiKey: process.env.GEMINI_API_KEY,
  model: 'gemini-1.5-flash',
});

// Generate insights
const insight = await aiClient.generateInsight('Analyze this product trend...');

// Compare competitors
const analysis = await aiClient.generateCompetitorAnalysis(
  creatorData,
  competitorData
);
```

## 🔄 Background Jobs

The worker application runs scheduled jobs:

- **Trend Computation**: Updates product trend scores every 15 minutes
- **Alert Firing**: Checks for alert triggers every 5 minutes
- **Data Cleanup**: Removes old data daily at 2 AM

## 📊 Database Schema

Key entities:
- **Users**: Creator accounts with TikTok Shop integration
- **Products**: TikTok Shop products with trend scores
- **Alerts**: User-defined trend score thresholds
- **AlertFires**: Triggered alerts for notification
- **Creators**: TikTok Shop creator profiles
- **Analytics**: User performance metrics

## 🚀 Deployment

### Production Build

```bash
pnpm build
```

### Environment Variables

Make sure to set all required environment variables in production:
- `DATABASE_URL`
- `GEMINI_API_KEY`
- `TIKTOK_SHOP_CLIENT_ID` and `TIKTOK_SHOP_CLIENT_SECRET` (for OAuth)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📝 License

This project is private and proprietary.

---

**Built with ❤️ for TikTok Shop creators**